#!/bin/bash
# 设置环境变量
export GOROOT=/home/<USER>/soft/go_1.19.3
export PATH=${GOROOT}/bin:${PATH}

# go proxy env
go env -w GO111MODULE="on"
go env -w GOPROXY=http://goproxy.duxiaoman-int.com/nexus/repository/goproxy.cn/
#GOPRIVATE会等价于GONOPROXY、GONOSUMDB两个变量，所以配置GOPRIVATE之后可以不用配置GONOPROXY、GONOSUMDB
go env -w GOPRIVATE="*.duxiaoman-int.com"
go env -w GONOPROXY="**.duxiaoman-int.com**"
go env -w GONOSUMDB="*"
go env -w "GOFLAGS"="-mod=mod"
# 下载supervise bin文件
wget -O output.tar.gz  "http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/supervise/1.0.0.2/output.tgz" && tar -zxvf output.tar.gz && mv output/bin/supervise ./ && rm -rf output.tar.gz
