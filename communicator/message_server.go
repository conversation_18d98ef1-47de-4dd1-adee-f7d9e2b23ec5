package communicator

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"sync"
	"sync/atomic"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/peer"

	"mdc-server/common"
)

//根据proto生成的communicator.pb.go文件，创建对应的grpc server数据结构
//该数据结构用于创建对应的RPC方法（GetMsg）
type MDCMsgReceiverServer struct {
	pb_server.MDCMsgReceiverServer
}

//创建rpc Server.
func NewGrpcServer() *grpc.Server {
	//初始化rpc server连接参数
	var server_opts []grpc.ServerOption

	//keepalive参数定义
	server_opts = append(server_opts,
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle: time.Duration(common.Config.ServerRpcKeepalive.ServerParam.MaxConnectionIdle) * time.Second,
			Time:              time.Duration(common.Config.ServerRpcKeepalive.ServerParam.Time) * time.Second,
			Timeout:           time.Duration(common.Config.ServerRpcKeepalive.ServerParam.Timeout) * time.Second}),
		grpc.KeepaliveEnforcementPolicy(keepalive.EnforcementPolicy{
			MinTime:             time.Duration(common.Config.ServerRpcKeepalive.EnforcementPolicy.MinimumTime) * time.Second,
			PermitWithoutStream: common.Config.ServerRpcKeepalive.EnforcementPolicy.PermitWithoutStream}))

	//初始化一个rpc server
	//输入实例化后的rpc结构，输出server指针
	grpcServer := grpc.NewServer(server_opts...)

	return grpcServer
}

//启动rpc Server
func StartGrpcServer(waitGroup *sync.WaitGroup, grpcServer *grpc.Server) {
	//得到监听器
	//输入网络协议，地址及端口号，输出对应的监听器
	lis, err := net.Listen("tcp4", fmt.Sprintf("0.0.0.0:%d",
		common.Config.RpcServer.Port))
	if err != nil {
		common.Log.Critical("ErrorMsg=[%v] CalledError=[%v] addr=[%v]",
			common.ErrRpcStartServer, err, fmt.Sprintf("0.0.0.0:%d", common.Config.RpcServer.Port))
		os.Exit(-1)
	}

	//将grpc接口注册到rpc server中
	//输入server指针，server proto接口
	pb_server.RegisterMDCMsgReceiverServer(grpcServer, &MDCMsgReceiverServer{})

	err = grpcServer.Serve(lis)
	if err != nil {
		common.Log.Critical("ErrorMsg=[%v] CalledError=[%v]", common.ErrRpcStartServer, err)
		StopGrpcServer(waitGroup, grpcServer)
		os.Exit(-1)
	}
}

//关闭rpc Server
func StopGrpcServer(waitGroup *sync.WaitGroup, grpcServer *grpc.Server) {
	//关闭rpc Server
	//等待已发起的的rpc请求处理完毕后再关闭
	grpcServer.GracefulStop()

	//如果消息队列还未关闭，则关闭消息队列
	if atomic.LoadInt32(&common.MsServerMsgChanIsClose) == 0 {
		atomic.AddInt32(&common.MsServerMsgChanIsClose, 1)
		//关闭全局Agent消息队列
		close(common.MsServerMsgChan)
	}

	//waitgroup操作
	waitGroup.Done()
}

// 异步任务发送到mdc-agent
func SendAsyncMsgToDagent(messageInfo pb_agent.MdcAgentAsyncMsg, clientAddr string) (*pb_agent.MdcAgentAsyncRespons, error) {
	common.Log.Info("[SendAsyncMsgToDagent] messageInfo=[%+v] clientAddr=[%v]", messageInfo, clientAddr)
	//dial参数初始化
	connCtx, connCancel := context.WithTimeout(context.Background(),
		time.Duration(common.Config.TcpConnTimeOut)*time.Millisecond)
	defer connCancel()
	conn, err := grpc.DialContext(connCtx, clientAddr, grpc.WithBlock(), grpc.WithInsecure(),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                time.Duration(common.Config.ServerRpcKeepalive.ClientParam.Time) * time.Second,
			Timeout:             time.Duration(common.Config.ServerRpcKeepalive.ClientParam.TimeOut) * time.Second,
			PermitWithoutStream: common.Config.ServerRpcKeepalive.ClientParam.PermitWithOutStream}))
	if err != nil {
		common.Log.Warning("fail to send message err=[%v]", err)
		return &pb_agent.MdcAgentAsyncRespons{}, err
	}
	defer conn.Close()
	//创建tcp连接
	client := pb_agent.NewMdcAgentClient(conn)
	//声明一个超时自动关闭的context
	reqCtx, reqCancel := context.WithTimeout(context.Background(),
		time.Duration(common.Config.AgentTimeOut)*time.Millisecond)
	defer reqCancel()
	//将消息通过rpc方法发送给receiver
	respons, err := client.ReceiveAsyncTaskMsgFromServer(reqCtx, &messageInfo)
	if err != nil {
		//输入context；输出数据流及错误信息
		common.Log.Warning("fail to send message err=[%v]", err)
	} else {
		common.Log.Info(" Send task msg to agent succeed, chat=[%v]", respons)
	}

	return respons, err
}

//同步任务发给mdc-agent
func SendSyncMsgToDagent(messageInfo pb_agent.MdcAgentSyncMsg, clientAddr string) (*pb_agent.MdcAgentSyncRespons, error) {
	//dial参数初始化
	connCtx, connCancel := context.WithTimeout(context.Background(),
		time.Duration(common.Config.TcpConnTimeOut)*time.Millisecond)
	defer connCancel()
	conn, err := grpc.DialContext(connCtx, clientAddr, grpc.WithBlock(), grpc.WithInsecure(),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                time.Duration(common.Config.ServerRpcKeepalive.ClientParam.Time) * time.Second,
			Timeout:             time.Duration(common.Config.ServerRpcKeepalive.ClientParam.TimeOut) * time.Second,
			PermitWithoutStream: common.Config.ServerRpcKeepalive.ClientParam.PermitWithOutStream}))
	if err != nil {
		common.Log.Warning("fail to send message err=[%v]. clientAddr=[%v] messageInfo=[%+v]", err, clientAddr, messageInfo)
		return &pb_agent.MdcAgentSyncRespons{}, err
	}
	defer conn.Close()
	//创建tcp连接
	client := pb_agent.NewMdcAgentClient(conn)
	//声明一个超时自动关闭的context
	reqCtx, reqCancel := context.WithTimeout(context.Background(),
		time.Duration(common.Config.AgentTimeOut)*time.Millisecond)
	defer reqCancel()
	//将消息通过rpc方法发送给receiver
	respons, err := client.ReceiveSyncTaskMsgFromServer(reqCtx, &messageInfo)
	if err != nil {
		//输入context；输出数据流及错误信息
		common.Log.Warning("fail to send message err=[%v]. clientAddr=[%v] messageInfo=[%+v]", err, clientAddr, messageInfo)
	} else {
		common.Log.Debug(" Send task msg to agent succeed. response=[%v]  clientAddr=[%v] messageInfo=[%+v]", respons, clientAddr, messageInfo)
	}

	return respons, err
}

func GetClientIP(ctx context.Context) (string, error) {
	pr, ok := peer.FromContext(ctx)
	if !ok {
		return "", errors.New("Invoke FromContext() failed")
	}
	if pr.Addr == net.Addr(nil) {
		return "", errors.New("Peer.Addr is nil")
	}
	// return addr like : "***********:12345"
	addr := pr.Addr.String()
	return addr, nil
}

//将任务写入消息队列 交由work协程处理
func PutMsMsgToChan(msMsg interface{}) error {
	//如果消息队列已满，则打印warning日志
	if cap(common.MsServerMsgChan) == len(common.MsServerMsgChan) {
		err := errors.New("msServer MsgChan buffer is full")
		common.Log.Warning("[MDC-Server] Warning Info=[%v]. MsServerMsgChan "+
			"length=[%v] cap=[%v] message context=[%v]", err, len(common.MsServerMsgChan), cap(common.MsServerMsgChan), msMsg)
	}
	//如果消息队列没有关闭，则写入
	if atomic.LoadInt32(&common.MsServerMsgChanIsClose) == 0 {
		common.MsServerMsgChan <- &common.MdcServerTaskInfo{msMsg}
	} else {
		err := errors.New("msServer MsgChan is closed")
		common.Log.Warning("[MDC-Server] Warning Info=[%v]."+
			"common.MsServerMsgChanIsClose: %v message context=[%v]",
			err, atomic.LoadInt32(&common.MsServerMsgChanIsClose), msMsg)
		return err
	}
	return nil
}

//处理前端异步任务
func (s *MDCMsgReceiverServer) HandleXwebPlusFrontAsyncMsg(ctx context.Context, xwebFontMsg *pb_server.XwebPlusFrontAsyncMsg) (*pb_server.MdcServerTaskResMsg, error) {
	clientIp, err := GetClientIP(ctx)
	if err != nil {
		common.Log.Warning("ErrorMsg=[%v] CalledError=[%v] ctx=[%+v].",
			common.ErrGetClientIp, err, ctx)
		return nil, err
	}

	//将消息放入消息队列
	err = PutMsMsgToChan(*xwebFontMsg)
	if err != nil {
		common.Log.Warning("ErrorMsg=[Tran Msg to task failed].CalledError=[%v]", err)
	}

	//构造Respons返回
	responsMsg := &pb_server.MdcServerTaskResMsg{
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   xwebFontMsg.GetBaseMessage().GetClusterId(),
			ClusterName: xwebFontMsg.GetBaseMessage().GetClusterName(),
			NodeId:      xwebFontMsg.GetBaseMessage().GetNodeId(),
		},
		ResponsNote: fmt.Sprintf("Receiver had received %v Message from front page.ip=[%v]",
			xwebFontMsg.GetMsgType(), clientIp),
	}

	return responsMsg, err
}

//处理agent汇报任务
func (s *MDCMsgReceiverServer) HandleMdcAgentReportMsg(ctx context.Context, mdcAgentReportMsg *pb_server.MdcAgentReportMsg) (*pb_server.MdcServerTaskResMsg, error) {
	clientIp, err := GetClientIP(ctx)
	if err != nil {
		common.Log.Warning("Failed to get clientIp from ctx=[%+v], err=[%v]", ctx, err)
		return nil, err
	}
	//将消息放入消息队列
	err = PutMsMsgToChan(*mdcAgentReportMsg)
	if err != nil {
		common.Log.Warning("ErrorMsg=[Tran Msg to task failed].CalledError=[%v]", err)
	}

	//构造Respons返回
	responsMsg := &pb_server.MdcServerTaskResMsg{
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   mdcAgentReportMsg.GetBaseMessage().GetClusterId(),
			ClusterName: mdcAgentReportMsg.GetBaseMessage().GetClusterName(),
			NodeId:      mdcAgentReportMsg.GetBaseMessage().GetNodeId(),
		},
		ResponsNote: fmt.Sprintf("Receiver had received %v Message from front page.ip=[%v]",
			mdcAgentReportMsg.GetMsgType(), clientIp),
	}
	return responsMsg, err
}
