package communicator

import (
	"context"
	"sync"
	"testing"
	"time"

	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
	"google.golang.org/grpc"

	"mdc-server/common"
)

//创建rpc Server
func TestNewGrpcServer(t *testing.T) {
	Convey("NewGrpcServer：创建rpc Server", t, func() {
		var serverType *grpc.Server
		So(NewGrpcServer(), ShouldHaveSameTypeAs, serverType)
	})
}

//启动&关闭 Server
func TestStartGrpcServer(t *testing.T) {
	Convey("启动&关闭 rpc Server", t, func() {

		//主协程等待变量
		var waitgroup sync.WaitGroup
		waitgroup.Add(1)
		grpcServer := NewGrpcServer()
		go StartGrpcServer(&waitgroup, grpcServer)
		StopGrpcServer(&waitgroup, grpcServer)

		So(grpcServer.GetServiceInfo(), ShouldBeEmpty)
	})
}

//将消息放到入tinker消息队列中
func TestPutMsgToChan(t *testing.T) {
	//变量声明
	frontMsg := pb_server.XwebPlusFrontAsyncMsg{
		TaskType: pb_server.MdcServerTaskType_START_BACKUP,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterName: "test",
			ClusterId:   1,
			NodeId:      "node2",
		},
		MsgType: &pb_server.XwebPlusFrontAsyncMsg_StartBackupTaskReqMsg{
			StartBackupTaskReqMsg: &pb_server.StartBackUpTaskReqMsg{
				BkType:         1,
				FullDay:        5,
				BkMysqlBasedir: "/tets/",
				BkStarTime:     "",
			},
		},
	}

	resposMsg := pb_server.MdcServerTaskResMsg{}
	Convey("PutMsgToChan：将消息放到入tinker消息队列中", t, func() {
		err := PutMsMsgToChan(&frontMsg)
		So(err, ShouldBeNil)
	})
	//写满队列
	for i := 0; len(common.MsServerMsgChan) < (cap(common.MsServerMsgChan) - 1); i++ {
		common.MsServerMsgChan <- &common.MdcServerTaskInfo{frontMsg}
	}

	contx, _ := context.WithTimeout(context.Background(), time.Second*1)
	conn, _ := grpc.DialContext(contx, "0.0.0.0:10000", grpc.WithBlock(), grpc.WithInsecure())
	client := pb_server.NewMDCMsgReceiverClient(conn)
	Convey("PutMsgToChan：队列写满报错", t, func() {
		err := PutMsMsgToChan(&frontMsg)
		So(err, ShouldBeNil)
		respons, err := client.HandleXwebPlusFrontAsyncMsg(contx, &frontMsg)
		So(respons, ShouldHaveSameTypeAs, &resposMsg)
		So(err, ShouldNotBeNil)
	})
}
