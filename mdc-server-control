#!/bin/bash
# 1. 开关
# 2. 环境变量
# 3. source文件
# 4. 常量

# stop等待优雅退出超时时间
readonly WAIT_MAX_TIME=10
readonly APP_HOME="$(cd "$(dirname "$0")"; pwd)"

# app的bin文件名，需要放置在${APP_HOME}/bin/目录下
# ${APP_HOME}/bin/${bin_name}
# ${APP_HOME}/bin/supervise.${bin_name}
bin_name="mdc-server"

# 配置文件的绝对路径
config_path="${APP_HOME}/conf/mdc-server.yaml"

# 5. 变量
pid_supervise=0
pid_bin_name=0

#######################################
# Brief:
#   get pid from (ps -ef) command
# Globals:
#   pid
# Arguments:
#   None
# Returns:
#   None
#######################################
function get_pid_online() {
    pid_bin_name="$(ps -ef | grep ${APP_HOME}/bin/${bin_name} | grep -v grep | grep -v ${APP_HOME}/bin/supervise.${bin_name} | awk '{print $2}' | head -n 1)"
    pid_supervise="$(ps -ef | grep ${APP_HOME}/bin/supervise.${bin_name} | grep -v grep | awk '{print $2}'| head -n 1)"
}

function get_app_pid_by_psef() {
    pid_bin_name="$(ps -ef | grep ${APP_HOME}/bin/${bin_name} | grep -v grep | grep -v ${APP_HOME}/bin/supervise.${bin_name} | awk '{print $2}'| head -n 1)"
}

#######################################
# Brief:
#   start
# Globals:
#   pid
# Arguments:
#   None
# Returns:
#   None
#######################################
function start() {
    get_app_pid_by_psef

    # 1.判断进程是否已经启动
    if [[ ! -z "${pid_bin_name}" ]]; then
        echo "[${bin_name} already started!](pid=${pid_bin_name})"
        exit 0
    fi

    # 2.通过supervise启动app
    ${APP_HOME}/bin/supervise.${bin_name} -p ${APP_HOME}/status/${bin_name} -f "${APP_HOME}/bin/${bin_name} -config=${config_path}" >> ${APP_HOME}/control.log 2>&1
    sleep 2

    # 3.通过ps -ef查找app的pid，检查启动是否成功
    get_app_pid_by_psef
    if [[ ! -z "${pid_bin_name}" ]]; then
        sleep 2
        if [ "${pid_bin_name}" == "$(ps -ef | grep ${APP_HOME}/bin/${bin_name} | grep -v grep | grep -v ${APP_HOME}/bin/supervise.${bin_name} | awk '{print $2}'| head -n 1)" ]; then
            echo "[${bin_name} start success!] (pid=${pid_bin_name})"
            exit 0
        else
            echo "[${bin_name} start failed! progress id was changed]"
            exit 1
        fi

    else
        echo "[${bin_name} start failed!] (could not found ${bin_name} progress)"
        exit 1
    fi
}

#######################################
# Brief:
#   stop
# Globals:
#   pid
# Arguments:
#   None
# Returns:
#   None
#######################################
function stop_app_graceful() {
    # 先尝试优雅退出
    skill -15 ${pid_bin_name}
    sleep 2
    get_app_pid_by_psef

    # 循环检查进程是否退出，如超过WAIT_MAX_TIME时间无法优雅退出成功，则kill -9服务进程退出
    local wait_time=0
    while [[ ! -z "${pid_bin_name}" ]]; do
        if [[ ${wait_time} -gt ${WAIT_MAX_TIME} ]]; then
            echo "[${bin_name} could not stop graceful, so kill -9 stop](pid=${pid_bin_name})" >&2
            skill -9 ${pid_bin_name}
        fi
        sleep 1
        (( wait_time += 1 ))
        get_app_pid_by_psef
    done
}

function stop() {
    # 1.停掉supervise进程
    get_pid_online
    while [[ ! -z "${pid_supervise}" ]]; do
        skill -9 ${pid_supervise}
        sleep 1
        get_pid_online
    done
    echo "[supervise.${bin_name} has been stoped success]"

    # 2.检查服务进程是否存在，尝试通过ps -ef方式获取pid
    get_app_pid_by_psef

    # 3.对status中记录的pid或ps -ef方式获取到的pid进行优雅退出
    if [[ -z "${pid_bin_name}" ]]; then
        echo "[${bin_name} has been stoped already]"
    else
        stop_app_graceful
        echo "[${bin_name} stop success!]"
    fi
}

function version() {
    ${APP_HOME}/bin/${bin_name} -v 2>&1
    exit 0
}

# usage
function usage() {
    echo "Usage: $0 {start|stop|restart|reload}"
}

function main() {
    case "$1" in
        'start')
            start
            ;;
        'stop')
            stop
            ;;
        'restart')
            stop
            start
            ;;
        '-h')
            usage
            ;;
        '-v')
            version
            ;;
        *)
            usage
            exit 1
    esac
    exit 0
}

# 7. 主函数/主逻辑
main "$@"
