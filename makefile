GOCMD=go
GOBUILD=$(GOCMD) build
GOBASE=$(shell pwd)
MAINFILE=$(GOBASE)/cmd/mdc-server.go
BINARYFILE=$(GOBASE)/bin/mdc-server
COMMIT_HASH=$(shell git rev-parse --short HEAD || echo "GitNotFound")
BUILD_DATE=$(shell date '+%Y-%m-%d %H:%M:%S')
BUILD_VERSION="1.1.1.0"
all: build
build:
	$(GOBUILD) -v -ldflags "-X \"main.BuildVersion=${COMMIT_HASH}\" -X \"main.BuildDate=$(BUILD_DATE)\"" -o $(BINARYFILE) $(MAINFILE)
clean:
	@rm -rf bin

