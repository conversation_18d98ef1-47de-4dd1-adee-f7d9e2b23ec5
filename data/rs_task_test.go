package data

import (
	"testing"

	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
)

func TestGetCleanRsDataTaskList(t *testing.T) {
	<PERSON>vey("GetCleanRsDataTaskList： 成功.", t, func() {
		rsList, err := GetCleanRsDataTaskList()
		So(rsList, ShouldBeNil)
		So(err, ShouldBeNil)
	})
}

func TestGetRsTaskInfo(t *testing.T) {
	Convey("GetRsTaskInfo： 成功.", t, func() {
		rsList, err := GetRsTaskInfo(1, 1,0)
		So(rsList, ShouldBeNil)
		So(err, ShouldBeNil)
	})
}

func TestUpdateRsTaskStatus(t *testing.T) {
	Convey("UpdateRsTaskStatus： 成功.", t, func() {
		err := UpdateRsTaskStatus(1, pb_server.RsTaskStatus_ExecRestoreSuccess, "测试")
		So(err, ShouldBeNil)
	})
}

func TestUpdateRsTaskBinlogPath(t *testing.T) {
	<PERSON><PERSON>("UpdateRsTaskBinlogPath： 成功.", t, func() {
		err := UpdateRsTaskBinlogPath(1, "home/mysql/mysql")
		So(err, ShouldBeNil)
	})
}
