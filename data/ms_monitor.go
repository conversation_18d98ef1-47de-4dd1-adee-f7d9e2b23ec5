package data

import (
	"errors"
	"fmt"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	dtglobal "dt-common/global"
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

//注册ms信息到DB中判断逻辑
/*
1、判断该mdc serverId在DB中是否存在，
2、存在，自我身份验证判断是自己则返回nil，不是报错退出
3、不存在，判断是否存在master ms ,
4、master ms 存在, 则将自己注册为slave ms
5、master ms 不存在, 则获取MsMaster表锁,将自己注册为slave
*/
func InitMsInfo(serverId int64, msHost string) error {
	ptrOrmer := orm.NewOrm()
	//判断serverId是否存在
	var result []string
	_, err := dt.RawQueryRows(ptrOrmer, &result, "SELECT mdc_server_host from mdc_server_monitor WHERE mdc_server_id = ? ", serverId)
	if err != nil {
		common.Log.Warn("Failed to get msInfo,serverId=[%v],reason=[%v] ", serverId, err)
		return err
	}
	//判断serverId是否存在
	if len(result) == 1 {
		err := isCheckMsRole(serverId, msHost)
		if err != nil {
			common.Log.Warn("Ms authentication failed,serverId=[%v]，reason=[%v]", serverId, err)
		}
	} else if len(result) <= 0 {
		//默认将自己注册为slave
		err = setMsToSlave(serverId, msHost, ptrOrmer)
		if err != nil {
			common.Log.Warn("Failed to register msRole,serverId=[%v],reason=[%v],", serverId, err)
		}
	}
	if nil == err {
		common.Log.Notice("ms has been successfully registered,serverId=[%v]", serverId)
	}
	return err
}

//通过serverId和host对ms身份进行判断
func isCheckMsRole(serverId int64, host string) error {
	msMonitor := dao_mdc.MsMonitor{
		MdcServerId: serverId,
	}
	err := msMonitor.ReadByIndexs()
	if err != nil {
		common.Log.Warn("Failed to get msMonitor from db,serverId=[%v],reason=[%v]", serverId, err)
		return err
	} else if msMonitor.MdbServerHost != host {
		errMsg := fmt.Sprintf("serverId is already occupied,serverId=[%v]", serverId)
		return errors.New(errMsg)
	}
	return nil
}

//将ms注册为slave ms
func setMsToSlave(serverId int64, msHost string, ptrOrmer orm.Ormer) error {
	msMonitor := dao_mdc.MsMonitor{
		Role:          uint8(common.MS_SLAVE),
		MdcServerId:   serverId,
		MdbServerPort: common.Config.RpcServer.Port,
		Status:        uint8(common.MS_NORMAL),
		MdbServerHost: msHost,
	}
	msMonitor.SetPtrOrmer(ptrOrmer)
	_, err := msMonitor.InsertOneRecord()
	if err != nil {
		common.Log.Warn("Failed to register ms info,serverId=[%v],reason=[%v]", serverId, err)
		return err
	}
	return nil
}

//将ms注册为master ms
func SetMsToMaster(serverId int64) error {
	// 开启事务
	ptrOrmer, beginTrxErr := BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Start transaction failed! reason=[%v]", beginTrxErr)
		return beginTrxErr
	}
	// 对ms_monitor添加表锁
	var msList []dao_mdc.MsMonitor
	_, err := dt.RawQueryRows(ptrOrmer, &msList, "select * from mdc_server_monitor for update")
	if err != nil {
		common.Log.Warn("Failed to get ms_monitor table lock,serverId=[%v],reason=[%v]", serverId, err)
		RollbackTrx(ptrOrmer)
		return err
	}
	// 持表锁成功二次判断master ms是否存在
	msMasterList := make([]dao_mdc.MsMonitor, 0, len(msList))
	for _, ms := range msList {
		if ms.Role == uint8(common.MS_MASTER) && ms.Status == uint8(common.MS_NORMAL) {
			msMasterList = append(msMasterList, ms)
		}
	}
	if len(msMasterList) == 1 {
		common.Log.Warn("Master ms has existed!, Role change failed. masterMs=[%v]", msMasterList[0])
		RollbackTrx(ptrOrmer)
		return nil
	} else if len(msMasterList) > 1 {
		errMsg := fmt.Sprintf("The master ms is not unique. masterMsList=[%v]", msMasterList)
		common.Log.Warn(errMsg)
		RollbackTrx(ptrOrmer)
		return errors.New(errMsg)
	}
	msMonitor := dao_mdc.MsMonitor{
		Role:        uint8(common.MS_MASTER),
		MdcServerId: serverId,
	}
	msMonitor.SetPtrOrmer(ptrOrmer)
	conds := []string{"MdcServerId"}
	cols := []string{"Role"}
	_, err = msMonitor.UpdateByCondCols(conds, cols)
	if err != nil {
		common.Log.Warn("Failed to register ms info ,reason=[%v]", err)
		//注册失败,释放锁资源
		RollbackTrx(ptrOrmer)
		return err
	}
	commitErr := CommitTrx(ptrOrmer)
	if commitErr != nil {
		common.Log.Warn("Trx Commit failed reason=[%v]", commitErr)
	}
	common.Log.Notice("Change ms role to Master cs is successfull. serverId=[%v]", serverId)
	return commitErr
}

//从数据库中获取自身角色,如果发现自身状态异常进行修复
func CheckRole(msId int64) (role uint8, err error) {
	//开启事务
	ptrOrmer, beginTrxErr := BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Start transaction failed! reason=[%v]", beginTrxErr)
		return 0, beginTrxErr
	}
	//获取ms当前状态信息
	err, msInfo := GetMsInfo(msId, ptrOrmer)
	if err != nil {
		common.Log.Warn("Failed to get ms role from db reason=[%v]", err)
		RollbackTrx(ptrOrmer)
		return 0, err
	}
	//对状态进行判断,异常状态修复
	if msInfo.Status == uint8(common.MS_ERROR) {
		//只要状态为异常，那么恢复正常后的角色一定为CS_SLAVE
		whereConds := []dtglobal.WhereConds{{Column: "mdc_server_id", Expr: dtglobal.Expr_Equal, Value: []interface{}{msId}}}
		columnSet := orm.Params{"role": uint8(common.MS_SLAVE), "status": uint8(common.MS_NORMAL)}
		updateCnt, err := dt.UpdateByConds(msInfo, whereConds, columnSet, ptrOrmer)
		if err != nil || 0 == updateCnt {
			err = errors.New(fmt.Sprintf("Failed to set status to normal .updateRows=[%v] reason=[%v]", updateCnt, err))
			RollbackTrx(ptrOrmer)
			return 0, err
		}
		//状态修改完成,事务提交
		commitErr := CommitTrx(ptrOrmer)
		if commitErr != nil {
			common.Log.Warn("Commit Trx failed reason=[%v]", commitErr)
			return 0, commitErr
		}
		common.Log.Notice("msId=[%v] role=[%v] was found to be in an abnormal state and now it has returned to normal",
			msId, common.MsRole(msInfo.Role))
		return uint8(common.MS_SLAVE), nil
	}
	commitErr := CommitTrx(ptrOrmer)
	if commitErr != nil {
		common.Log.Warn("Commit Trx failed reason=[%v]", commitErr)
		return 0, commitErr
	}
	return msInfo.Role, nil
}

//检查当前自身的角色状态
func GetMsInfo(msId int64, ptrOrmer orm.Ormer) (err error, msMonitor *dao_mdc.MsMonitor) {
	var msInfo []*dao_mdc.MsMonitor
	//当前读获取最新的ms信息
	_, err = dt.RawQueryRows(ptrOrmer, &msInfo, "SELECT * from mdc_server_monitor WHERE mdc_server_id = ?  for update", msId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetMsInfo, err)
		return err, nil
	}
	//msId为唯一性索引，要么行数为1 要么为0
	if len(msInfo) <= 0 {
		errMsg := fmt.Sprintf("Faile to get msInfo,MsId=[%v]", msId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), nil
	}
	return nil, msInfo[0]
}

//竞争获取Slave Timer的锁权限
func GetSlaveTimerLock(ptrOrmer orm.Ormer, msId int64) error {
	var result []*dao_mdc.MsMonitor
	//竞争异常Slave Timer的行锁
	_, err := dt.RawQueryRows(ptrOrmer, &result, "SELECT id from mdc_server_monitor WHERE mdc_server_id = ? and status = ? and role = ? for update",
		msId,
		uint8(common.MS_NORMAL),
		uint8(common.MS_SLAVE))
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetMsSlaveLock, err)
		return err
	}
	//确认查询的ms是否存在
	if len(result) != 1 {
		errMsg := fmt.Sprintf("Cs information checks for anomalies！ms_id=[%v]", msId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//竞争获取Master Timer的锁权限
func GetMasterTimerLock(ptrOrmer orm.Ormer) (oldMsId int64, err error) {
	var result []*dao_mdc.MsMonitor
	//竞争Master Timer的行锁
	_, err = dt.RawQueryRows(ptrOrmer, &result, "SELECT * from mdc_server_monitor WHERE role = ? and status = ? for update",
		uint8(common.MS_MASTER),
		uint8(common.MS_NORMAL))
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v] ", common.ErrGetMsMasterLock, err)
		return -1, err
	}
	if len(result) != 1 {
		errMsg := fmt.Sprintf("ErrorMsg=[%v],The number of MasterMs is not unique! ", common.ErrGetMsMasterLock)
		common.Log.Warn(errMsg)
		return -1, errors.New(errMsg)
	}
	oldMsId = result[0].MdcServerId
	//获取成功将事务指针以及msId返回
	return oldMsId, nil
}

//执行继承MasterTimer role的一系列操作
/*
1、将自身角色变更为new master Timer
2、将old masterTimer 置为异常
*/
func InheritMasterTimerRole(msid int64, oldMsId int64, ptrOrmer orm.Ormer) error {
	//变更自己为Master Timer
	_, err := dt.RawUpdate(ptrOrmer, "UPDATE mdc_server_monitor SET role = ? WHERE mdc_server_id = ? ", uint8(common.MS_MASTER), msid)
	if err != nil {
		common.Log.Warn("Failed to inherit MasterTimer Role, reason=[%v]", err)
		return err
	}
	//将old MasterTimer 状态变更为异常状态
	_, err = dt.RawUpdate(ptrOrmer, "UPDATE mdc_server_monitor SET status = ? WHERE mdc_server_id = ? ", uint8(common.MS_ERROR), oldMsId)
	if err != nil {
		common.Log.Warn("Failed to Set oldMasterTimer to exception state, reason=[%v]", err)
		return err
	}
	return nil
}

//处理异常状态slaveTimer,将slaveTimer状态变更为异常状态
func InheritSlaveTimerRole(slaveMsId int64, ptrOrmer orm.Ormer) error {
	//将slaveTimer 状态变更为异常状态
	_, err := dt.RawUpdate(ptrOrmer, "UPDATE mdc_server_monitor SET status = ? WHERE mdc_server_id = ? ", uint8(common.MS_ERROR), slaveMsId)
	if err != nil {
		common.Log.Warn("Failed to Set slaveTimer to exception state, reason=[%v]", err)
		return err
	}
	return nil
}
