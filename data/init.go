package data

import (
	"database/sql"
	"errors"
	"fmt"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	"github.com/astaxie/beego/orm"
	"mdc-server/common"
)

const (
	MAX_UNIQUE_ID  = ********* // 唯一键最大值
	WARM_UNIQUE_ID = 10000     // 唯一键预警值
)

func InitDao() error {
	//注册数据库驱动
	orm.RegisterDriver(common.Config.RegisterDatabase, orm.DRMySQL)
	//注册数据库
	err := registerDataBase()
	if err != nil {
		panic(err)
	}
	//需要在init中注册定义的model
	orm.RegisterModel(new(dt.DbInstance))
	orm.RegisterModel(new(dao_mdc.MsMonitor))
	orm.RegisterModel(new(dao_mdc.BkTaskInfo))
	orm.RegisterModel(new(dao_mdc.BkBinlogExecRecord))
	orm.RegisterModel(new(dao_mdc.BkXtraTaskExecRecord))
	orm.RegisterModel(new(dao_mdc.RSTaskRecord),new(dao_mdc.RSSubTaskRecord))
	//开发阶段，开始orm的debug模式，打印SQL日志
	//适用config.go中的配置开关
	orm.Debug = common.Config.OrmDebugSwitch

	return err
}

// 初始化数据库连接配置
func registerDataBase() error {
	var databaseurl string
	if common.Config.OnlineDSN.Disable == false {
		//注册mdc_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.OnlineDSN.MdcDbUser, common.Config.OnlineDSN.MdcDbPassword, common.Config.OnlineDSN.MdcDbAddr,
			common.Config.OnlineDSN.MdcDbSchema, common.Config.OnlineDSN.Charset)
		err := orm.RegisterDataBase("default", common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register mdc db failed, err=[%v]", err)
			goto ErrReturn
		}
		//注册tinker_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.OnlineDSN.TinkerDbUser, common.Config.OnlineDSN.TinkerDbPassword, common.Config.OnlineDSN.TinkerDbAddr,
			common.Config.OnlineDSN.TinkerDbSchema, common.Config.OnlineDSN.Charset)
		err = orm.RegisterDataBase(common.Config.OnlineDSN.TinkerDbSchema, common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register tinker db failed, err=[%v]", err)
			goto ErrReturn
		}
		//注册fdb_tinker_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.OnlineDSN.FdbTinkerDbUser, common.Config.OnlineDSN.FdbTinkerDbPassword, common.Config.OnlineDSN.FdbTinkerDbAddr,
			common.Config.OnlineDSN.FdbTinkerDbSchema, common.Config.OnlineDSN.Charset)
		err = orm.RegisterDataBase(common.Config.OnlineDSN.FdbTinkerDbSchema, common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register fdb server db failed, err=[%v]", err)
			goto ErrReturn
		}
		return nil
	} else if common.Config.TestDSN.Disable == false {
		//注册mdc_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.TestDSN.MdcDbUser, common.Config.TestDSN.MdcDbPassword, common.Config.TestDSN.MdcDbAddr,
			common.Config.TestDSN.MdcDbSchema, common.Config.TestDSN.Charset)
		err := orm.RegisterDataBase("default", common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register test mdc db failed, err=[%v]", err)
			goto ErrReturn
		}
		//注册tinker_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.TestDSN.TinkerDbUser, common.Config.TestDSN.TinkerDbPassword, common.Config.TestDSN.TinkerDbAddr,
			common.Config.TestDSN.TinkerDbSchema, common.Config.TestDSN.Charset)
		err = orm.RegisterDataBase(common.Config.OnlineDSN.TinkerDbSchema, common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register test tinker db failed, err=[%v]", err)
			goto ErrReturn
		}
		//注册fdb_tinker_db
		databaseurl = fmt.Sprintf("%v:%v@tcp(%v)/%v?charset=%v&loc=Local",
			common.Config.TestDSN.FdbTinkerDbUser, common.Config.TestDSN.FdbTinkerDbPassword, common.Config.TestDSN.FdbTinkerDbAddr,
			common.Config.TestDSN.FdbTinkerDbSchema, common.Config.TestDSN.Charset)
		err = orm.RegisterDataBase(common.Config.OnlineDSN.FdbTinkerDbSchema, common.Config.RegisterDatabase,
			databaseurl, common.Config.MaxIdleConns, common.Config.MaxOpenConns)
		if err != nil {
			common.Log.Error("register test fdb server db failed, err=[%v]", err)
			goto ErrReturn
		}
		return nil
	}
ErrReturn:
	return errors.New("online-dsn and test-dsn are all nil in mdc_server.yaml!")
}

//根据不同的db名 获得不同的orm
func GetDataBaseOrmer(dbName, driverName string) (orm.Ormer, error) {
	var (
		errStr string
		err    error
		ormer  orm.Ormer
		db     *sql.DB
	)
	if db, err = orm.GetDB(dbName); err != nil {
		errStr = fmt.Sprintf("failed to get meta db, err=[%v]", err)
		return nil, errors.New(errStr)
	}
	if ormer, err = orm.NewOrmWithDB(driverName, dbName, db); err != nil {
		errStr = fmt.Sprintf("failed to get meta db ormer, err=[%v]", err)
		return nil, errors.New(errStr)
	}
	return ormer, nil
}
