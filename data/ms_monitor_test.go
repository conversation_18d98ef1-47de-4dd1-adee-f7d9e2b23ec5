package data

import (
	"testing"

	db_mdc "dt-common/dao/mdc"
	"github.com/astaxie/beego/orm"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func init() {
	//注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)
	//注册数据库
	orm.RegisterDataBase("default", "mysql",
		"test:test123@tcp(127.0.0.1:3306)/mdc_db?charset=utf8mb4", 30, 100)
	//需要在init中注册定义的model
	orm.RegisterModel(new(db_mdc.MsMonitor))
}

func TestInitMsInfo(t *testing.T) {
	Convey("InitMsInfo： 成功.", t, func() {
		b := InitMsInfo(123, "127.0.0.1")
		So(b, ShouldBeNil)
	})
}

func TestSetMsToMaster(t *testing.T) {
	Convey("SetMsToMaster： 成功.", t, func() {
		b := SetMsToMaster(123)
		So(b, ShouldBeNil)
	})
}

func TestCheckRole(t *testing.T) {
	Convey("CheckRole： 成功.", t, func() {
		_, b := CheckRole(123)
		So(b, ShouldBeNil)
	})
}

func TestGetMasterTimerLock(t *testing.T) {
	Convey("GetMasterTimerLock： 成功.", t, func() {
		ptrOrmer, beginTrxErr := BeginTrx()
		if beginTrxErr != nil {
			common.Log.Warn("Start transaction failed! reason=[%v]", beginTrxErr)
		}
		_, err := GetMasterTimerLock(ptrOrmer)
		So(err, ShouldBeNil)
	})
}
