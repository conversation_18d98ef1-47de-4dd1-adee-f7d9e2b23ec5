package data

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestBeginTrx(t *testing.T) {
	Convey("beginTrx： o.Begin() fail.", t, func() {
		_, err := BeginTrx()
		So(err, ShouldBeNil)
	})
}
func TestCommitTrx(t *testing.T) {
	o, _ := BeginTrx()
	Convey("commitTrx： o.Commit() fail.", t, func() {
		err := CommitTrx(o)
		So(err, ShouldBeNil)
	})
}
func TestRollbackTrx(t *testing.T) {
	o, _ := BeginTrx()
	<PERSON>vey("rollbackTrx： o.Rollback() successful.", t, func() {
		err := RollbackTrx(o)
		So(err, ShouldBeNil)
	})
	Convey("rollbackTrx： o.Rollback() fail.", t, func() {
		err := RollbackTrx(o)
		So(err, ShouldNotBeNil)
	})
}
