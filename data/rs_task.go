package data

import (
	"fmt"
	"time"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	pb_server "dt-common/protobuf/mdc-server"
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

//获得可以清理中转数据的恢复记录
func GetCleanRsDataTaskList() (err error, rsTaskInfo []*dao_mdc.RSTaskRecord) {
	ptrOrmer := orm.NewOrm()
	//查询恢复任务信息表 找到恢复完成且需要清理数据的恢复任务
	_, err = dt.RawQueryRows(ptrOrmer, &rsTaskInfo, "SELECT * from rs_task_info WHERE restore_status = ? ", common.BS_ONLINE)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetRsTaskInfo, err)
		return err, nil
	}
	if len(rsTaskInfo) > 0 {
		return nil, rsTaskInfo
	}
	return nil, nil
}

//根据集群id和任务id获得恢复任务信息
func GetRsTaskInfo(clustreId int64, nodeId int64, taskId int64) (err error, bkTaskInfo *dao_mdc.RSTaskRecord) {
	ptrOrmer := orm.NewOrm()
	rsTaskInfoList := make([]*dao_mdc.RSTaskRecord, 0)
	//查询日常xtra备份任务表 找到上传华南bos成功且满足清理时间的热备任务
	_, err = dt.RawQueryRows(ptrOrmer, &rsTaskInfoList, "SELECT * from rs_task_info WHERE cluster_id = ? and node_id = ? and id = ?", clustreId, nodeId, taskId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetXtraTaskInfo, err)
		return err, nil
	}
	if len(rsTaskInfoList) == 1 {
		return nil, rsTaskInfoList[0]
	}
	return nil, nil
}

//根据taskId修改rsTask的状态
func UpdateRsTaskStatus(taskId int64, status pb_server.RsTaskStatus, conment string) error {
	bkExecLog := fmt.Sprintf("time=[%v]  %v\n", time.Now().Format(common.TIME_FORMAT), conment)
	//需要更新的列
	_, err := dt.RawExecSql(nil, "update rs_task_info set restore_exec_log=concat(restore_exec_log, ?), restore_status = ? , task_update_time = ? where id = ?",
		bkExecLog, status, time.Now().Format(common.TIME_FORMAT), taskId)
	if err != nil {
		common.Log.Warn("failed to update xtraTask info. TaskId=[%v] err=[%v]", taskId, err)
		return err
	}
	return nil
}

//根据taskId修改日志
func UpdateRsTaskExecLog(taskId int64, comment string) error {
	bkExecLog := fmt.Sprintf("time=[%v]  %v\n", time.Now().Format(common.TIME_FORMAT), comment)
	//需要更新的列
	_, err := dt.RawExecSql(nil, "update rs_task_info set restore_exec_log=concat(restore_exec_log, ?), task_update_time = ? where id = ?",
		bkExecLog, time.Now().Format(common.TIME_FORMAT), taskId)
	if err != nil {
		common.Log.Warn("failed to update xtraTask info. TaskId=[%v] err=[%v]", taskId, err)
		return err
	}
	return nil
}

//根据taskId修改binlogSqlPath
func UpdateRsTaskBinlogPath(taskId int64, dataPath string) error {
	rsTask := new(dao_mdc.RSTaskRecord)
	rsTask.Id = taskId
	rsTask.BinlogSqlPath = dataPath
	//需要更新的列
	conds := []string{"Id"}
	cols := []string{"BinlogSqlPath"}
	_, err := rsTask.UpdateByCondCols(conds, cols)
	if err != nil {
		common.Log.Warn("failed to update rsTask info. TaskId=[%v] err=[%v]", rsTask.Id, err)
		return err
	}
	return nil
}
