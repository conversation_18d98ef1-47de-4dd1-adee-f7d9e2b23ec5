package data

import (
	pb_server "dt-common/protobuf/mdc-server"
	"errors"
	"fmt"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

//获得状态为online的备份任务
func GetOnlineTaskList() (err error, bkTaskInfo []*dao_mdc.BkTaskInfo) {
	ptrOrmer := orm.NewOrm()
	//查询备份任务信息表 找到状态为上线状态的备份任务
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfo, "SELECT * from bk_task_info WHERE bk_exec_status = ? ", common.BS_ONLINE)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBkTaskInfo, err)
		return err, nil
	}
	if len(bkTaskInfo) > 0 {
		return nil, bkTaskInfo
	}
	return nil, nil
}

//根据集群id找到对应的备份任务
func GetBkTaskInfo(clusterId int64, nodeId int64) (err error, bkTaskInfo *dao_mdc.BkTaskInfo) {
	ptrOrmer := orm.NewOrm()
	var bkTaskInfoList []*dao_mdc.BkTaskInfo
	//查询备份任务信息表
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_task_info WHERE cluster_id = ? and node_id = ?", clusterId, nodeId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBkTaskInfo, err)
		return err, nil
	}

	if len(bkTaskInfoList) != 1 {
		errMsg := fmt.Sprintf("Failed to get bkTaskInfo, clusterId=[%v], nodeId=[%v]", clusterId, nodeId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), nil
	}
	return nil, bkTaskInfoList[0]
}

//查找开启binlog备份的备份任务并且状态为初始化状态
func GetBinlogTaskOnlineList() (err error, bkTaskInfo []*dao_mdc.BkTaskInfo) {
	ptrOrmer := orm.NewOrm()
	//查询备份任务信息表 找到binlog备份任务开启的集群
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfo, "SELECT * from bk_task_info WHERE binlog_process_status = ? and binlog_check_status = ?",
		common.BT_ONLINE, pb_server.BinlogCheckStatus_CheckInit)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBkTaskInfo, err)
		return err, nil
	}
	if len(bkTaskInfo) > 0 {
		return nil, bkTaskInfo
	}

	return nil, nil
}

//更新备份任务的ProtocolId
func UpdateBkInfoProtocolId(clusterId int64, nodeId int64, protocolId int64) (err error) {
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = clusterId
	bkInfo.NodeId = nodeId
	bkInfo.ProtocolId = protocolId
	//需要更新的列
	conds := []string{"ClusterId", "NodeId"}
	cols := []string{"ProtocolId"}
	_, err = bkInfo.UpdateByCondCols(conds, cols)
	if err != nil {
		common.Log.Warn("failed to update xtraTask info. clusterId=[%v] err=[%v]", clusterId, err)
		return err
	}
	return nil
}

// 更新备份任instanceID
func UpdateBkTaskInstanceId(clusterId ,nodeId, instanceId int64) error {
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = clusterId
	bkInfo.NodeId = nodeId
	bkInfo.InstanceId = instanceId
	_, err := bkInfo.UpdateByCondCols([]string{"ClusterId", "NodeId"}, []string{"InstanceId"})
	if err != nil {
		errMsg := fmt.Sprintf("failed to update backupTask info. clusterId=[%v] nodeId=[%v] err=[%v]", clusterId, nodeId, err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}
