package data

import (
	"testing"

	"dt-common/dao"
	"github.com/astaxie/beego/orm"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func init() {
	//注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)
	//注册数据库
	orm.RegisterDataBase("tinker", "mysql",
		"test:test123@tcp(127.0.0.1:3306)/tinker?charset=utf8mb4", 30, 100)
	orm.RegisterDataBase("FDB_cortex_server", "mysql",
		"test:test123@tcp(127.0.0.1:3306)/FDB_cortex_server?charset=utf8mb4", 30, 100)
	//需要在init中注册定义的model
	orm.RegisterModel(new(dao.DbInstance))
}

func TestGetClusterInstance(t *testing.T) {
	common.Config.OnlineDSN.FdbTinkerDbSchema = "FDB_cortex_server"
	Convey("GetClusterInstance： 成功.", t, func() {
		_, b := GetClusterInstance(1, 0, 0, true)
		So(b, ShouldBeNil)
	})
	Convey("GetClusterInstance： 失败.", t, func() {
		_, b := GetClusterInstance(1, 0, 5, true)
		So(b, ShouldNotBeNil)
	})
	common.Config.OnlineDSN.TinkerDbSchema = "tinker"
	Convey("GetClusterInstance： 成功.", t, func() {
		_, b := GetClusterInstance(8172, 0, 1, false)
		So(b, ShouldBeNil)
	})
	Convey("GetClusterInstance： 失败.", t, func() {
		_, b := GetClusterInstance(1, 0, 5, false)
		So(b, ShouldNotBeNil)
	})
}
