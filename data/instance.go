package data

import (
	"errors"
	"fmt"

	dt "dt-common/dao"
	pb_tinker "dt-common/protobuf/tinker"
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

//获得实例信息
func GetClusterInstance(clusterId int64, nodeId int64, role pb_tinker.InstanceRole, isFdb bool) (*dt.DbInstance, error) {
	backup := new(dt.DbInstance)
	var ormer orm.Ormer
	var err error
	if isFdb {
		ormer, err = GetDataBaseOrmer(common.Config.OnlineDSN.FdbTinkerDbSchema, "mysql")
		if err != nil {
			errMsg := fmt.Sprintf("GetDataBaseOrmer failed, error=[%v]", err)
			return nil, errors.New(errMsg)
		}
		backup.SetPtrOrmer(ormer)
	} else {
		ormer, err = GetDataBaseOrmer(common.Config.OnlineDSN.TinkerDbSchema, "mysql")
		if err != nil {
			errMsg := fmt.Sprintf("GetDataBaseOrmer failed, error=[%v]", err)
			return nil, errors.New(errMsg)
		}
		backup.SetPtrOrmer(ormer)
	}
	err = dt.RawQueryRow(ormer, &backup, "select * from db_instances where cluster_id = ? and node_id = ? and role = ? ",
		clusterId, nodeId, int32(role))
	if err != nil {
		common.Log.Warn("get backup info by clusterId failed. clusterId=[%v] err=[%v]", clusterId, err)
		return nil, err
	}
	return backup, nil
}
