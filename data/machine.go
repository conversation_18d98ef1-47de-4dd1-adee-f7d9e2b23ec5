package data

import (
	"dt-common/global"
	"github.com/astaxie/beego/orm"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	"mdc-server/common"
)

//根据机器类型获取机器列表
func GetMachineList(mType common.MachineType, env global.DbsEnv) ([]dao_mdc.MachineInfo, error) {
	var machineList []dao_mdc.MachineInfo
	ptrOrmer := orm.NewOrm()
	_, err := dt.RawQueryRows(ptrOrmer, &machineList, "select * from mdc_machine_info where machine_type = ? and env=?", mType, env)
	if err != nil {
		common.Log.Warn("get machine info by machine type failed.err=[%v]", err)
		return nil, err
	}
	return machineList, nil
}

//// 获取当前天备份任务命中大磁盘命中次数
//func GegBigDiskMachine(bigDiskMachine string) (int, error) {
//	var xtraTaskList []dao_mdc.BkXtraTaskExecRecord
//	ptOrmer := orm.NewOrm()
//	_, err := dt.RawQueryRows(ptOrmer, &xtraTaskList, " select * from bk_xtratask_exec_record where "+
//		"bk_start_time >= ? and bk_exec_status != ? and bk_remote_ip=?", time.Now().Format(common.TIME_SDAY_FORMAT), pb_server.XtraTaskStatus_XtraTaskAbandon,
//		bigDiskMachine)
//	if err != nil {
//		common.Log.Warn("Failed to obtain the number of hits of the big disk, err=[%v]", err)
//		return -1, err
//	}
//	return len(xtraTaskList), err
//}

// 大磁盘是否可用用于备份
func IsBigDiskMachineAvailable(instanceIP string) bool {
	var machineList []dao_mdc.MachineInfo
	ptrOrmer := orm.NewOrm()
	_, err := dt.RawQueryRows(ptrOrmer, &machineList, "select * from mdc_machine_info where machine_ip = ? and machine_type = ?", instanceIP, common.BACKUP)
	if err != nil || len(machineList) < 1 {
		common.Log.Warn("get machine info by machine ip failed.err=[%v]", err)
		return false
	}
	return true
}
