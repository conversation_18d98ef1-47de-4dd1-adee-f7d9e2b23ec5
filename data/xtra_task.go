package data

import (
	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	dt_global "dt-common/global"
	pb_server "dt-common/protobuf/mdc-server"
	tinker_pb "dt-common/protobuf/tinker"
	"errors"
	"fmt"
	"github.com/astaxie/beego/orm"
	"strconv"
	"time"

	"mdc-server/common"
)

// 获得可以清理备份的日常备份任务列表
func GetCleanXtraDataTaskList() (err error, bkTaskInfo []*dao_mdc.BkXtraTaskExecRecord) {
	deadTime := time.Now().AddDate(0, 0, -common.Config.RemoteDataSaveDays)
	ptrOrmer := orm.NewOrm()
	//查询日常xtra备份任务表 找到上传华南bos成功且满足清理时间，且华南bos目录不为空的热备任务
	// 备份记录已备份结束时间为基准
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfo, "SELECT * from bk_xtratask_exec_record WHERE bk_exec_status = ? and bk_end_time < ? and siod_file_path !='' ", pb_server.XtraTaskStatus_UploadSiodBosSuccess, deadTime)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetXtraTaskInfo, err)
		return err, nil
	}
	if len(bkTaskInfo) > 0 {
		return nil, bkTaskInfo
	}
	return nil, nil
}

// 获得可以清理华北bos的日常备份任务列表
func GetCleanBOSDataTaskList() (err error, bkTaskInfo []*dao_mdc.BkXtraTaskExecRecord) {
	deadTime := time.Now().AddDate(0, 0, -common.Config.DbaBosSaveDays)
	ptrOrmer := orm.NewOrm()
	// 查询日常xtra备份任务表 找到大磁盘已清理状态且满足清理周期记录
	// 备份记录已备份结束时间为基准
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfo, "SELECT * from bk_xtratask_exec_record WHERE bk_exec_status = ? and bk_end_time < ? and siod_file_path !='' ", pb_server.XtraTaskStatus_RemoteBkDataClean, deadTime)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetXtraTaskInfo, err)
		return err, nil
	}
	if len(bkTaskInfo) > 0 {
		return nil, bkTaskInfo
	}
	return nil, nil
}

// 获得某个集群最近一条备份记录
func GetClusterLatestRecord(clustreId int64, nodeId int64) (err error, bkTaskInfo *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	//查询日常xtra备份任务表 找到上传华南bos成功且满足清理时间的热备任务
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? order by bk_start_time desc limit 1", clustreId, nodeId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetXtraTaskInfo, err)
		return err, nil
	}
	if bkTaskInfoList != nil && len(bkTaskInfoList) != 0 {
		return nil, bkTaskInfoList[0]

	}
	return nil, nil
}

// 获得某个集群最近一次成功备份的记录(备份状态上传至DBA_BOS认为任务备份集合可用)
func GetClusterLatestSuccessRecord(clustreId int64, nodeId int64, taskStatus pb_server.XtraTaskStatus) (err error, bkTaskInfo *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and bk_exec_status > ? order by bk_start_time desc limit 1", clustreId, nodeId, taskStatus)
	if err != nil || len(bkTaskInfoList) != 1 {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],bkTaskInfoList=[%v],clustreId=[%v],nodeId=[%v],taskStatus=[%d]", common.ErrGetXtraTaskInfo, err, bkTaskInfoList, clustreId, nodeId, taskStatus)
		return err, nil
	}
	return nil, bkTaskInfoList[0]
}

// 根据集群id和任务id获得备份任务信息
func GetXtraTaskInfo(clustreId int64, nodeId int64, taskId int64) (error, *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	// 根据taskId,clusterId,nodeId获取备份任务记录
	_, err := dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and task_id = ?", clustreId, nodeId, taskId)
	if err != nil || len(bkTaskInfoList) != 1 {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v] taskId=[%v] clusterId=[%v] nodeId=[%v]", common.ErrGetXtraTaskInfo, err, taskId, clustreId, nodeId)
		return err, nil
	}
	return nil, bkTaskInfoList[0]
}

// 添加一条备份记录
func AddXtraTaskRecord(bkMysqlIp string, remoteIp string,
	bkInfo dao_mdc.BkTaskInfo, lsnFrom string, isFullDay bool, cycleStatus int) (*dao_mdc.BkXtraTaskExecRecord, error) {
	//构建xtra_exec_record记录
	newXtraRecord := new(dao_mdc.BkXtraTaskExecRecord)
	newXtraRecord.ClusterId = bkInfo.ClusterId
	newXtraRecord.ClusterName = bkInfo.ClusterName
	newXtraRecord.NodeId = bkInfo.NodeId
	newXtraRecord.BkPath = bkInfo.BkMysqlBasedir
	newXtraRecord.BkRemoteIp = remoteIp
	newXtraRecord.BkMysqlIp = bkMysqlIp
	newXtraRecord.BkOpt = bkInfo.BkOpt
	newXtraRecord.BkExecStatus = int32(pb_server.XtraTaskStatus_InitStatus)
	dataPath := fmt.Sprintf("%v_%v_%v/%v", bkInfo.ClusterName, bkInfo.ClusterId, bkInfo.NodeId, time.Now().Format(common.TIME_DAY_FORMAT))
	newXtraRecord.BkRemotePath = fmt.Sprintf("%v/%v", common.RemoteBaseDir, dataPath)
	// 兼容原MDC单机集群BOS存储路径()
	if bkInfo.ClusterType == int32(tinker_pb.ClusterType_SINGLE) {
		bosPath := fmt.Sprintf("%v_%v/%v", bkInfo.ClusterName, bkInfo.ClusterId, time.Now().Format(common.TIME_DAY_FORMAT))
		newXtraRecord.BkBosPath = bosPath
		// DDBS改为新MDC存储形式
	} else {
		newXtraRecord.BkBosPath = dataPath
	}
	newXtraRecord.BkCreateTime = fmt.Sprintf("%v %v", time.Now().Format(common.TIME_SDAY_FORMAT), bkInfo.BkStartTime)
	newXtraRecord.BkStartTime = time.Now().Format(common.TIME_FORMAT)
	newXtraRecord.BkEndTime = time.Now().Format(common.TIME_FORMAT)
	newXtraRecord.BkUpdateTime = time.Now().Format(common.TIME_FORMAT)
	newXtraRecord.BkExecLog = fmt.Sprintf("time=[%v]  %v\n", time.Now().Format(common.TIME_FORMAT), "mdc begin to deal xtra task")
	// 全备/空lsn/初始Lsn 都进行全备
	if isFullDay || lsnFrom == common.EmptyLsnPos || lsnFrom == common.InitLsnPos {
		newXtraRecord.BkType = int32(common.FullHotBk)
		newXtraRecord.BkCycleStatus = 0
		newXtraRecord.LsnFrom = "0"
		newXtraRecord.BkRemotePath = fmt.Sprintf("%v%v.%v", newXtraRecord.BkRemotePath, common.FullHotBkStr, common.UnpackType)
		newXtraRecord.BkBosPath = fmt.Sprintf("%v%v.%v", newXtraRecord.BkBosPath, common.FullHotBkStr, common.UnpackType)
	} else {
		newXtraRecord.BkType = int32(common.IncHotBk)
		newXtraRecord.BkCycleStatus = int32(cycleStatus)
		newXtraRecord.LsnFrom = lsnFrom
		newXtraRecord.BkRemotePath = fmt.Sprintf("%v%v%v.%v", newXtraRecord.BkRemotePath, common.IncHotBkStr, strconv.Itoa(cycleStatus), common.UnpackType)
		newXtraRecord.BkBosPath = fmt.Sprintf("%v%v%v.%v", newXtraRecord.BkBosPath, common.IncHotBkStr, strconv.Itoa(cycleStatus), common.UnpackType)
	}
	// 如果重试5次依然没有拿到唯一的taskId,则报错
	// todo:取消taskID设计 直接改为用主键做唯一标识
	for idx := 0; idx < 5; idx++ {
		// 生成唯一的taskId
		taskId := dt_global.UniqueInt64()
		newXtraRecord.TaskId = taskId
		// 备份任务记录插入
		_, err := newXtraRecord.InsertOneRecord()

		// 插入失败重试
		if err != nil && idx < 4 {
			common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetXtraTaskInfo, err)
			// 重试前sleep 1s
			time.Sleep(time.Second * 1)
			continue
		}

		// 重试超过最大值
		if err != nil && idx == 4 {
			common.Log.Warn("ErrorMsg=[fail to get unique taskId retry times "+
				"more than 5] CalledError=[%v] taskId=[%v]", err, taskId)
			return nil, err
		}

		// 插入成功
		if err == nil {
			return newXtraRecord, nil
		}
	}
	errMsg := fmt.Sprintf("Failed to insert backup task record Error unknown")
	common.Log.Warn(errMsg)
	return nil, errors.New(errMsg)
}

// 根据taskId修改xtraTask的状态
func UpdateXtraTaskStatus(taskId int64, status pb_server.XtraTaskStatus, conment string) error {
	bkExecLog := fmt.Sprintf("time=[%v]  %v\n", time.Now().Format(common.TIME_FORMAT), conment)
	//需要更新的列
	_, err := dt.RawExecSql(nil, "update bk_xtratask_exec_record set bk_exec_log=concat(bk_exec_log, ?) , bk_exec_status = ? , bk_update_time  = ? where task_id = ?",
		bkExecLog, int32(status), time.Now().Format(common.TIME_FORMAT), taskId)
	if err != nil {
		common.Log.Warn("failed to update xtraTask info. TaskId=[%v] err=[%v]", taskId, err)
		return err
	}
	return nil
}

// GetLastXtraBaseTask 获得请求恢复时间的最近一条BASE记录
// 1.任务状态为dba bos上传成功之后
// 2.任务状态不为废弃/不为dba bos上传失败
func GetLastXtraBaseTask(clustreId int64, nodeId int64, rsTime string) (error, *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	placeholder := "?"
	for i := 1; i < len(common.AbnormalDBABOSArray); i++ {
		placeholder += ",?"
	}

	_, err := dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and bk_type = ? and bk_end_time <= ? and bk_exec_status>=? and bk_exec_status not in ("+placeholder+") order by bk_end_time desc limit 1",
		clustreId, nodeId, common.FullHotBk, rsTime, int32(pb_server.XtraTaskStatus_UploadDbaBosSuccess), common.AbnormalDBABOSArray)
	if err != nil || len(bkTaskInfoList) != 1 {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],bkTaskInfoList=[%v],clustreId=[%v],nodeId=[%v],rsTime=[%v]", common.ErrGetXtraTaskInfo, err, bkTaskInfoList, clustreId, nodeId, rsTime, int32(pb_server.XtraTaskStatus_XtraTaskAbandon))
		return err, nil
	}
	common.Log.Info("[GetLastXtraBaseTask] clusterId=[%v] nodeId=[%v] rsTime=[%v] bkTaskInfoList=[%+v]", clustreId, nodeId, rsTime, bkTaskInfoList)
	return nil, bkTaskInfoList[0]
}

// GetRangeXtraList 获得基于提供baseDataTime 之后的记录
// 1.任务状态为dba bos上传成功之后
// 2.任务状态不为废弃/不为dba bos上传失败
func GetRangeXtraList(clustreId int64, nodeId int64, baseDataTime string, rsTime string) (error, []*dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	// 用于打印日志
	bkTmpTaskInfoList := make([]dao_mdc.BkXtraTaskExecRecord, 0)
	placeholder := "?"
	for i := 1; i < len(common.AbnormalDBABOSArray); i++ {
		placeholder += ",?"
	}
	// 上传dba bos 成功后的状态
	_, err := dt.RawQueryRows(ptrOrmer, &bkTaskInfoList,
		"SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and bk_create_time >= ? and bk_create_time <= ? and bk_exec_status >= ? and bk_exec_status not in ("+placeholder+") order by bk_create_time ",
		clustreId, nodeId, baseDataTime, rsTime, int32(pb_server.XtraTaskStatus_UploadDbaBosSuccess), common.AbnormalDBABOSArray)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],bkTaskInfoList=[%v],clustreId=[%v],nodeId=[%v],baseDataTime=[%v],rsTime=[%v]", common.ErrGetXtraTaskInfo, err, bkTaskInfoList, clustreId, nodeId, baseDataTime, rsTime, int32(pb_server.XtraTaskStatus_XtraTaskAbandon))
		return err, nil
	}
	for _, infolist := range bkTaskInfoList {
		bkTmpTaskInfoList = append(bkTmpTaskInfoList, *infolist)
	}
	common.Log.Info("[GetRangeXtraList] clusterId=[%v] nodeId=[%v] baseDataTime=[%v] rsTime=[%v] bkTmpTaskInfoList=[%+v]", clustreId, nodeId, baseDataTime, rsTime, bkTmpTaskInfoList)
	return nil, bkTaskInfoList
}

// 获得大于某个时间的最近的一条备份记录;且任务状态不是废弃
func GetNextXtraTask(clustreId int64, nodeId int64, createTime string) (error, *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	_, err := dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and bk_create_time > ? and bk_exec_status != ? order by bk_create_time desc limit 1", clustreId, nodeId, createTime, int32(pb_server.XtraTaskStatus_XtraTaskAbandon))
	if err != nil || len(bkTaskInfoList) != 1 {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],bkTaskInfoList=[%v],clusterID=[%v],nodeId=[%v],rsTime=[%v]", common.ErrGetXtraTaskInfo, err, bkTaskInfoList, clustreId, nodeId, createTime)
		return err, nil
	}
	return nil, bkTaskInfoList[0]
}

// 获取全备之后增量备份记录;且任务状态不是废弃
func GetIncrTask(clustreId int64, nodeId int64, createTime string) (error, *dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	_, err := dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from bk_xtratask_exec_record WHERE cluster_id = ? and node_id = ? and bk_create_time > ? and bk_type = ? and bk_exec_status != ? order by bk_create_time asc limit 1", clustreId, nodeId, createTime,
		common.IncHotBk, int32(pb_server.XtraTaskStatus_XtraTaskAbandon))
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],bkTaskInfoList=[%v], clusterID=[%v],nodeId=[%v], baseCreateTime=[%v]", common.ErrGetXtraTaskInfo, err, bkTaskInfoList, clustreId, nodeId, createTime)
		return err, nil
	}
	// 判断结果是否为空
	if len(bkTaskInfoList) == 0 {
		return nil, nil
	}
	return nil, bkTaskInfoList[0]
}

// 获取前一天未成功上传或未进行上传SIOD BOS集合信息
func GetFailUploadSiod() (error, []*dao_mdc.BkXtraTaskExecRecord) {
	ptrOrmer := orm.NewOrm()
	uploadFailList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	_, err := dt.RawQueryRows(ptrOrmer, &uploadFailList, "SELECT * FROM bk_xtratask_exec_record where bk_exec_status in (?, ?) and bk_create_time < ? and bk_bos_bucket= ? ",
		int32(pb_server.XtraTaskStatus_UploadSiodBosFail), int32(pb_server.XtraTaskStatus_UploadDbaBosSuccess), time.Now().AddDate(0, 0, -1).Format(common.TIME_SDAY_FORMAT),
		common.Config.DbaBosBucket)

	if err != nil {
		common.Log.Error("ErrorMsg=[%v] CalledError=[%v], uploadFailList=[%v], taskCreateTime=[%v]", common.ErrGetXtraTaskInfo, err, uploadFailList,
			time.Now().AddDate(0, 0, -1).Format(common.TIME_SDAY_FORMAT))
		return err, nil
	}
	return nil, uploadFailList
}

// 根据clusterID及nodeID获取当前备份任务
func GetXtraTaskForCluster(clusterID, nodeID int64) (error, *dao_mdc.BkTaskInfo) {
	ptrOrmer := orm.NewOrm()
	xtraTaskList := make([]*dao_mdc.BkTaskInfo, 0)
	_, err := dt.RawQueryRows(ptrOrmer, &xtraTaskList, "SELECT * FROM bk_task_info where cluster_id=? and node_id=?",
		clusterID, nodeID)
	if err != nil {
		common.Log.Error("ErrorMsg=[%v] CalledError=[%v], xtraTaskList=[%v], clusterID=[%v] nodeID=[%v]", common.ErrGetXtraTaskInfo, err, xtraTaskList,
			clusterID, nodeID)
		return err, nil
	}
	// 集群信息不唯一(每个集群只有一个备份任务）
	if len(xtraTaskList) != 1 {
		errMsg := fmt.Sprintf("xtra task info is not uniq, clusterID=[%v] nodeID=[%v]", clusterID, nodeID)
		common.Log.Error(errMsg)
		return errors.New(errMsg), nil
	}

	return nil, xtraTaskList[0]
}
