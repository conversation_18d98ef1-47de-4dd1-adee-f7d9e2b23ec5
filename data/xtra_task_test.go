package data

import (
	"testing"

	dao_mdc "dt-common/dao/mdc"
	pb_server "dt-common/protobuf/mdc-server"

	. "github.com/smartystreets/goconvey/convey"
)

func TestAddXtraTaskRecord(t *testing.T) {
	Convey("AddXtraTaskRecord： 成功.", t, func() {
		bkInfo := &dao_mdc.BkTaskInfo{
			ClusterName: "test",
			ClusterId:   1,
			ClusterType: 1,
			NodeId:      0,
		}

		xtraRecord, err := AddXtraTaskRecord("127.0.0.1", "10.3.4.8", *bkInfo, "", true, 0)
		So(err, ShouldBeNil)
		So(xtraRecord.LsnFrom, ShouldEqual, "")

		xtraRecord, err = AddXtraTaskRecord("127.0.0.1", "10.3.4.8", *bkInfo, "123", false, 1)
		So(err, ShouldBeNil)
		So(xtraRecord.LsnFrom, ShouldEqual, "0")

	})
}

func TestGetCleanXtraDataTaskList(t *testing.T) {
	Convey("GetCleanXtraDataTaskList： 成功.", t, func() {
		rsList, err := GetCleanXtraDataTaskList()
		So(rsList, ShouldBeNil)
		So(err, ShouldBeNil)
	})
}

func TestGetCleanBOSDataTaskList(t *testing.T) {
	Convey("GetCleanBOSDataTaskList： 成功.", t, func() {
		rsList, err := GetCleanBOSDataTaskList()
		So(rsList, ShouldBeNil)
		So(err, ShouldBeNil)
	})
}

func TestGetClusterLatestRecord(t *testing.T) {
	Convey("GetClusterLatestRecord： 成功.", t, func() {
		err, rsList := GetClusterLatestRecord(1, 0)
		So(err, ShouldBeNil)
		So(rsList, ShouldNotBeNil)
	})
}

func TestGetClusterLatestSuccessRecord(t *testing.T) {
	Convey("GetClusterLatestSuccessRecord： 成功.", t, func() {
		rsList, err := GetClusterLatestSuccessRecord(1, 0, pb_server.XtraTaskStatus_UploadDbaBosSuccess)
		So(err, ShouldBeNil)
		So(rsList, ShouldBeNil)
	})
}

func TestGetXtraTaskInfo(t *testing.T) {
	Convey("GetXtraTaskInfo： 成功.", t, func() {
		err, rsList := GetXtraTaskInfo(1, 0, 808811008)
		So(rsList, ShouldNotBeNil)
		So(rsList.TaskId, ShouldEqual, 808811008)
		So(err, ShouldBeNil)
	})
}

func TestUpdateXtraTaskStatus(t *testing.T) {
	Convey("UpdateXtraTaskStatus： 成功.", t, func() {
		err := UpdateXtraTaskStatus(1, pb_server.XtraTaskStatus_UploadSiodBosFail, "修改状态")
		So(err, ShouldBeNil)
	})
}

func TestGetLastXtraBaseTask(t *testing.T) {
	Convey("GetLastXtraBaseTask： 成功.", t, func() {
		err, rsList := GetLastXtraBaseTask(1, 0, "2021-08-17 17:24:03")
		So(err, ShouldBeNil)
		So(rsList, ShouldNotBeNil)
	})
}

func TestGetRangeXtraList(t *testing.T) {
	Convey("GetRangeXtraList： 成功.", t, func() {
		err, rsList := GetRangeXtraList(1, 0, "2021-08-17 17:14:03", "2021-08-17 17:14:23")
		So(err, ShouldBeNil)
		So(rsList, ShouldNotBeNil)
	})
}

func TestGetNextXtraTask(t *testing.T) {
	Convey("GetNextXtraTask： 成功.", t, func() {
		err, rsList := GetNextXtraTask(1, 0, "2021-08-17 17:14:23")
		So(rsList, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})
}
