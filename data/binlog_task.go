package data

import (
	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	pb_server "dt-common/protobuf/mdc-server"
	"errors"
	"fmt"
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

func AddBinlogRecord(mysqlIp string, baseMsg *pb_server.MdcBaseMessage, checkBinlogStatusRes *pb_server.CheckBinlogStatusReport) (*dao_mdc.BkBinlogExecRecord, error) {
	binlogRecord := new(dao_mdc.BkBinlogExecRecord)
	binlogRecord.ClusterId = baseMsg.GetClusterId()
	binlogRecord.ClusterName = baseMsg.GetClusterName()
	binlogRecord.NodeId = baseMsg.GetNodeId()
	binlogRecord.BinlogFileName = checkBinlogStatusRes.BinlogFileNameResult
	binlogRecord.BinlogStartTime = checkBinlogStatusRes.BeginUpdateTime
	binlogRecord.BinlogEndTime = checkBinlogStatusRes.EndUpdateTime
	binlogRecord.BkMysqlBinlogPath = checkBinlogStatusRes.BkBinlogPath
	binlogRecord.RemoteIp = checkBinlogStatusRes.RemoteIp
	binlogRecord.RemotePath = checkBinlogStatusRes.RemotePath
	binlogRecord.BkMysqlIp = mysqlIp
	binlogRecord.BinlogTaskStatus = int32(pb_server.BinlogRecordStatus_UploadRemoteSuccess)
	if _, err := binlogRecord.InsertOneRecord(); err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBinlogTaskInfo, err)
		return nil, err
	}
	return binlogRecord, nil
}

//获得某个集群最近的一条binlog备份记录
func GetBinlogLatestRecord(clustreId int64, nodeId int64, status pb_server.BinlogRecordStatus) (err error, bkTaskInfo *dao_mdc.BkBinlogExecRecord) {
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkBinlogExecRecord, 0)
	//查询日常binlog备份记录表
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from binlog_task_record WHERE cluster_id = ? and node_id = ? and binlog_task_status >= ? order by binlog_end_time desc limit 1",
		clustreId, nodeId, status)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBinlogTaskInfo, err)
		return err, nil
	}

	if len(bkTaskInfoList) == 1 {
		return nil, bkTaskInfoList[0]

	}
	return nil, nil
}

//更新binlogRecord信息
func UpdateBinlogRecordInfo(idList []int64, status pb_server.BinlogRecordStatus, remoteIp string, remotePath string) error {
	str := "?"
	for i := 1; i < len(idList); i++ {
		str += ",?"
	}
	ptrOrmer := orm.NewOrm()
	_, err := dt.RawUpdate(ptrOrmer, "UPDATE binlog_task_record SET binlog_task_status = ?,remote_ip = ?,remote_path = ?  WHERE id in ("+str+")",
		status, remoteIp, remotePath, idList)
	if err != nil {
		common.Log.Warn("Failed to UpdateBinlogRecordStatus, err=[%v]", err)
		return err
	}
	return nil
}

//更新binlogRecord状态
func UpdateBinlogRecordStatus(idList []int64, status pb_server.BinlogRecordStatus) error {
	str := "?"
	for i := 1; i < len(idList); i++ {
		str += ",?"
	}
	ptrOrmer := orm.NewOrm()
	_, err := dt.RawUpdate(ptrOrmer, "UPDATE binlog_task_record SET binlog_task_status =? WHERE id in ("+str+")",
		status, idList)
	if err != nil {
		common.Log.Warn("Failed to UpdateBinlogRecordStatus, err=[%v]", err)
		return err
	}
	return nil
}

//通过clusterId和状态获得binlogRecord数据
func GetBinlogRecordList(clustreId int64, nodeId int64, statusList []pb_server.BinlogRecordStatus) (err error, bkTaskInfo []*dao_mdc.BkBinlogExecRecord) {
	str := "?"
	for i := 1; i < len(statusList); i++ {
		str += ",?"
	}
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkBinlogExecRecord, 0)
	//查询日常binlog备份记录表
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from binlog_task_record WHERE cluster_id = ? and node_id = ? and binlog_task_status in ("+str+")",
		clustreId, nodeId, statusList)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBinlogTaskInfo, err)
		return err, nil
	}
	return nil, bkTaskInfoList
}

//通过时间范围获得binlogRecord数据
func GetBinlogRecordRangeList(clustreId int64, nodeId int64, statusList []pb_server.BinlogRecordStatus, startTime string, endTime string) (err error, bkTaskInfo []*dao_mdc.BkBinlogExecRecord) {
	str := "?"
	for i := 1; i < len(statusList); i++ {
		str += ",?"
	}
	ptrOrmer := orm.NewOrm()
	bkTaskInfoList := make([]*dao_mdc.BkBinlogExecRecord, 0)
	//查询binlog备份记录表
	_, err = dt.RawQueryRows(ptrOrmer, &bkTaskInfoList, "SELECT * from binlog_task_record WHERE cluster_id = ? and node_id = ? and binlog_task_status in ("+str+") and binlog_end_time >= ? and binlog_end_time <= ?",
		clustreId, nodeId, statusList, startTime, endTime)
	if err != nil || len(bkTaskInfoList) == 0 {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetBinlogTaskInfo, err)
		return err, nil
	}
	return nil, bkTaskInfoList
}

// 获取binlog已上传大磁盘成功记录
func GetUploadBinlogRecord() ([]dao_mdc.BkBinlogExecRecord, error) {
	ptrOrmer := orm.NewOrm()
	binlogTask := make([]dao_mdc.BkBinlogExecRecord, 0)
	_, err := dt.RawQueryRows(ptrOrmer ,&binlogTask , "select * from binlog_task_record where binlog_task_status = ? and " +
		"binlog_start_time > date_sub(curdate(),interval 2 day) and binlog_start_time < now()", pb_server.BinlogRecordStatus_UnpackWithXtraSuccess)
	if err != nil {
		common.Log.Warn("failed to get binlog for packing backup, err=[%v]", err)
		return nil , err
	}

	return binlogTask, nil
}

// 更新binlog备份任务探测状态
func UpdateCheckBinlogStatus(clusterId, nodeId int64, checkStatus pb_server.BinlogCheckStatus) error {
	ptOrmer := orm.NewOrm()
	_, err := dt.RawUpdate(ptOrmer, "update bk_task_info set binlog_check_status = ? where cluster_id=? and node_id=?", checkStatus, clusterId, nodeId)
	if err != nil {
		errMsg := fmt.Sprintf("failed update binlog task status, err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}