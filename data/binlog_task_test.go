package data

import (
	pb_agent "dt-common/protobuf/mdc-agent"
	"testing"
	"time"

	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func TestAddBinlogRecord(t *testing.T) {
	Convey("GetOnlineTaskList： 成功.", t, func() {
		baseMsg := &pb_server.MdcBaseMessage{
			ClusterId:   1,
			ClusterName: "test",
			NodeId:      0,
		}
		fileMsg := &pb_agent.CheckBinlogStatusRes{
			BinlogFileNameResult: "mysql-bin.002587",
			BeginUpdateTime:      "",
			BinlogServerId:       0,
		}
		recordInfo, err := AddBinlogRecord("127.0.0.1", baseMsg, fileMsg)
		So(err, ShouldBeNil)
		So(recordInfo.ClusterName, ShouldEqual, "test")
	})
}

func TestGetBinlogLatestRecord(t *testing.T) {
	Convey("GetBinlogRecordInfo： 成功.", t, func() {
		err, recordInfo := GetBinlogLatestRecord(1, 0, pb_server.BinlogRecordStatus_BkSuccess)
		So(err, ShouldBeNil)
		So(recordInfo.ClusterName, ShouldEqual, "test")
	})
	Convey("GetBinlogRecordInfo： 失败.", t, func() {
		err, recordInfo := GetBinlogLatestRecord(2, 0, pb_server.BinlogRecordStatus_BkSuccess)
		So(err, ShouldBeNil)
		So(recordInfo, ShouldBeNil)
	})
}

func TestUpdateBinlogRecordInfo(t *testing.T) {
	Convey("UpdateBinlogRecordInfo： 成功.", t, func() {
		err := UpdateBinlogRecordInfo([]int64{1, 2}, pb_server.BinlogRecordStatus_UploadRemoteSuccess, "10.12.34.8", "/home/<USER>")
		So(err, ShouldBeNil)
	})

}

func TestUpdateBinlogRecordStatus(t *testing.T) {
	Convey("UpdateBinlogRecordStatus： 成功.", t, func() {
		err := UpdateBinlogRecordStatus([]int64{1, 2}, pb_server.BinlogRecordStatus_UploadRemoteSuccess)
		So(err, ShouldBeNil)
	})
}

func TestGetBinlogRecordList(t *testing.T) {
	Convey("GetBinlogRecordList： 成功.", t, func() {
		err, recordList := GetBinlogRecordList(1, 0, []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess})
		So(err, ShouldBeNil)
		So(recordList, ShouldNotBeNil)
	})

	Convey("GetBinlogRecordList： 失败.", t, func() {
		err, recordList := GetBinlogRecordList(2, 0, []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess})
		So(err, ShouldBeNil)
		So(recordList, ShouldBeNil)
	})
}

func TestGetBinlogRecordRangeList(t *testing.T) {
	Convey("GetBinlogRecordRangeList： 成功.", t, func() {
		err, recordList := GetBinlogRecordRangeList(1, 0, []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}, common.TIME_FORMAT, time.Now().String())
		So(err, ShouldBeNil)
		So(recordList, ShouldBeNil)
	})

	Convey("GetBinlogRecordRangeList： 失败.", t, func() {
		err, recordList := GetBinlogRecordRangeList(2, 0, []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}, common.TIME_FORMAT, time.Now().String())
		So(err, ShouldBeNil)
		So(recordList, ShouldBeNil)
	})
}
