package data

import (
	"testing"

	db_mdc "dt-common/dao/mdc"
	"github.com/astaxie/beego/orm"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func init() {
	//注册数据库驱动
	orm.RegisterDriver("mysql", orm.DRMySQL)
	//注册数据库
	orm.RegisterDataBase("default", "mysql",
		"test:test123@tcp(127.0.0.1:3306)/mdc_db?charset=utf8mb4", 30, 100)
	//需要在init中注册定义的model
	orm.RegisterModel(new(db_mdc.MachineInfo))
	orm.RegisterModel(new(db_mdc.BkXtraTaskExecRecord))
	orm.RegisterModel(new(db_mdc.BkTaskInfo))
	orm.RegisterModel(new(db_mdc.RSTaskRecord))
	orm.RegisterModel(new(db_mdc.BkBinlogExecRecord))
	common.LoggerInit()
}

func TestGetMachineList(t *testing.T) {
	Convey("GetMachineList： 成功.", t, func() {
		infoList, err := GetMachineList(0)
		So(len(infoList), ShouldEqual, 1)
		So(err, ShouldBeNil)
	})
	Convey("GetMachineList： 失败.", t, func() {
		infoList, b := GetMachineList(1)
		So(len(infoList), ShouldEqual, 0)
		So(b, ShouldBeNil)
	})
}
