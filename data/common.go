package data

import (
	"github.com/astaxie/beego/orm"

	"mdc-server/common"
)

//开启事务
func BeginTrx() (orm.Ormer, error) {
	o := orm.NewOrm()
	err := o.Begin()
	if err != nil {
		common.Log.Warn("o.Begin() failed! ,reason=[%v]", err)
		return nil, err
	}
	return o, nil
}

//提交事务
func CommitTrx(o orm.Ormer) error {
	err := o.Commit()
	if err != nil {
		common.Log.Warn("o.commit() failed! ,reason=[%v]", err)
		o.Rollback()
		return err
	}
	return nil
}

//回滚事务
func RollbackTrx(o orm.Ormer) error {
	err := o.Rollback()
	if err != nil {
		common.Log.Warn(" o.Rollback() failed! ,reason=[%v]", err)
		return err
	}
	common.Log.Notice(" o.Rollback() successfuly!")
	return nil
}
