package data

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGetOnlineTaskList(t *testing.T) {
	Convey("GetOnlineTaskList： 成功.", t, func() {
		err, infoList := GetOnlineTaskList()
		So(len(infoList), ShouldEqual, 1)
		So(err, ShouldBeNil)
	})
}

func TestGetBkTaskInfo(t *testing.T) {
	Convey("GetBkTaskInfo： 成功.", t, func() {
		err, infoList := GetBkTaskInfo(193827, 1)
		So(infoList, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})
	Convey("GetBkTaskInfo： 失败.", t, func() {
		err, infoList := GetBkTaskInfo(8712, 1)
		So(infoList, ShouldBeNil)
		So(err, ShouldNotBeNil)
	})
}

func TestGetBinlogTaskOnlineList(t *testing.T) {
	Convey("GetBinlogTaskOnlineList： 成功.", t, func() {
		err, infoList := GetBinlogTaskOnlineList()
		So(len(infoList), ShouldEqual, 0)
		So(err, ShouldBeNil)
	})
}

func TestUpdateBkInfoProtocolId(t *testing.T) {
	Convey("UpdateBkInfoProtocolId： 成功.", t, func() {
		err := UpdateBkInfoProtocolId(193827, 1,123)
		So(err, ShouldBeNil)
	})
}
