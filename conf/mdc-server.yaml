# 线上必填，mdc-serevr-id
mdc-server-id: 12346
# 线上必填，线上环境被监控的数据库配置，对应测试环境为test-dsn
online-dsn:
  mdc_db_addr: 127.0.0.1:3306
  mdc_db_schema: mdc_db
  mdc_db_user: test
  mdc_db_password: test123
  tinker_db_addr: 127.0.0.1:3306
  tinker_db_schema: tinker
  tinker_db_user: test
  tinker_password: test123
  fdb_tinker_db_addr: 127.0.0.1:3306
  fdb_tinker_db_schema: FDB_cortex_server
  fdb_tinker_db_user: test
  fdb_tinker_db_password: test123
  charset: utf8mb4
  disable: true
# 测试环境配置
test-dsn:
  mdc_db_addr: 10.157.22.101:8501
  mdc_db_schema: mdc_db
  mdc_db_user: ks
  mdc_db_password: ks123
  tinker_db_addr: 10.157.22.101:8366
  tinker_db_schema: tinker
  tinker_db_user: dba_manager_rw
  tinker_db_password: hGFhvFdHABT12
  fdb_tinker_db_addr: 10.21.176.114:8888
  fdb_tinker_db_schema: FDB_cortex_server
  fdb_tinker_db_user: zhaixiaona
  fdb_tinker_db_password: zhaixiaona
  charset: utf8mb4
  disable: false
# 数据库连接超时时间，单位秒
conn-time-out: 1
# 数据库SQL执行超时时间，单位秒
query-time-out: 28800

########################################
#####        mdc-server 日志配置项        #######
########################################
# 日志级别，默认为3:Error
# [0:Emergency, 1:Alert, 2:Critical, 3:Error, 4:Warning, 5:Notice, 6:Informational, 7:Debug]
log-level: 6
# 日志最大保留天数
log-maxdays: 7
# 日志输出位置，默认日志输出到控制台
# 目前只支持['console', 'file']两种形式，如非console形式这里需要指定文件的路径，可以是相对路径
log-output: /Users/<USER>/Go/src/mdc-server/logs/mdc-server.log
# 任务日志入数据库，chan的大小
log-chan-size: 10

# mdc server心跳监控时间间隔
mdc-server-monitor-cron-step: 10
# 等待上次备份任务完成的最大超时时间
mdc-server-monitor-cron-timeout: 12
# mdc server心跳超时判断时间差s
mdc-server-monitor-heartbeat-timeout: 120
binlog-process-detect: 600
clean_data_step: 3600
remote_data_save_days: 7
dba_bos_save_days: 30
siod_bos_save_days: 180
# 定时重试上传siod间隔(单位:秒)
retry_upload_siod_step: 600

########################################
#####       grpc消息传递置项     ######
########################################
#线上必填，rpc server配置
rpc-server:
  addr: 127.0.0.1
  port: 10000
  hostname: testhost
  # http连接创建时握手交互超时时间
  connection-timeout: 35
# 客户端rpc交互channel超时时间,毫秒
agent-timeout: 180000
# 可缓存未处理的异步任务的最大数量，如果写满，则打印warn到日志中
agent-msg-chan-buffer: 1000
# rpc函数正常重试次数
func-retry-count: 3
# rpc函数最大重试次数
func-max-retry-count: 5
# rpc函数重试超时时间,毫秒
func-retry-interval-time: 1000
# tcp建连接的超时时间,
connect-timeout: 5000
server-rpc-keepalive:
  client-param:
    # 如果在该时间内，client未发现活跃连接，则对server发送ping包探活
    # 最小设置10s，小于10s则自动设置为1分钟
    time: 120
    # 从keepalive ping包发起时间点开始timeout时间内如果未发生rpc交互，则判定连接为关闭状态
    time-out: 35
    # 如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
    # 如果设置为false，如果没有活跃的rpc请求，则不做连接探活 =>出于资源消耗考虑选false
    permit-without-stream: false
  server-param:
    # 连接最大空闲时长，超过则关闭连接
    max-connection-idle: 30
    # 如果在该时间内，server未发现活跃连接，则对client发送ping包探活
    # 最小设置10s，小于10s则自动设置为1分钟
    time: 120
    # client端ping超时等待时间，即使没有活跃连接或连接已经关闭，也依然等待这个时间走完。
    time-out: 35
  enforcement-policy:
    # client端发出探活包的最小等待时间
    minimum-time: 100
    # 如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
    # 如果设置为false，如果没有活跃的rpc请求，client端发过来
    # ping包后server会返回连接关闭标志并关闭连接 =>出于资源消耗考虑选false
    permit-without-stream: false

# 创建协程数量
routine-number: 100
# Worker协程数
worker-number: 60
# Worker单个协程空闲超时时间
worker-chan-timeout: 10
# 一台大磁盘机器一天最多承接备份任务
# 修改此参数需要确认当前可用大磁盘机器数,大磁盘机器数*任务数 > 总备份任务数
remote—instance-hit-num: 35

dba_bos_bucket: dxm-dbbk-hb-test
siod_bos_bucket: dxm-dbbk-hb-test
siod_backup_manual_run: http://10.252.32.205:8400/backup-recovery-engine/api/v1/task/manualRun
siod_backup_auto_register_protocol: http://10.252.32.205:8400/backup-recovery-engine/api/v1/protocol/autoRegisterProtocol
siod_register_token: 8fe88ed93864f9020e860c7140b053ad
siod_backup_plaform: dbs