package common

import (
	"flag"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestConfig(t *testing.T) {
	Convey("解析配置文件", t, func() {
		var testConfigData *string = flag.String("config", "../conf/mdc-server.yaml", "kinshard config file")
		flag.Parse()

		err := ParseConfig(*testConfigData)
		if err != nil {
			t.Fatal(err)
		}
		So(Config.MdcServerId, ShouldNotBeNil)
		So(err, ShouldBeNil)
	})
}
