package common

var (
	//ms主从探活相关错误
	ErrRegisterMsMonitor = "fail to register ms heartbeat"
	ErrGetMsMasterLock   = "fail to get msMaster lock"
	ErrGetMsInfo         = "fail to get msMonitor info"
	ErrGetMsSlaveLock    = "fail to get msSlave lock"
	ErrMonitorMsMaster   = "fail to monitor msMaster"
	ErrMonitorMsSlave    = "fail to monitor msSlave"
	ErrAbnormalMsRole    = "Abnormal msRole!"
	ErrRegisterMs        = "fail to register ms"
	ErrGetLocalIp        = "fail to get local ip"

	//bk_task_info相关错误
	ErrGetBkTaskInfo = "fail to get bkTask info"

	//rs_task_info相关错误
	ErrGetRsTaskInfo = "fail to get rsTask info"

	//xtra_task_info相关错误
	ErrGetXtraTaskInfo = "fail to get xtraTask info"

	//binlog_task_info相关错误
	ErrGetBinlogTaskInfo = "fail to get binlogTask info"

	ErrMsMsgToChannel = "fail to put  msMsg to MsgChan."

	ErrTypeTransToJsonBytes = "Transport type to json bytes failed."
	ErrRpcNoRespons         = "call rpc received function failed."
	ErrRpcSendMsg           = "fail to send message."
	ErrRpcStartServer       = "fail to start grpc server."
	ErrGetClientIp          = "fail to get client ip."
)
