package common

import (
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"os"
	"time"

	"gopkg.in/yaml.v2"
)

const TIME_FORMAT = "2006-01-02 15:04:05"
const TIME_DAY_FORMAT = "20060102"
const TIME_SDAY_FORMAT = "2006-01-02"

type Configuration struct {
	OnlineDSN        *dsn `yaml:"online-dsn"`     // 线上环境数据库配置
	TestDSN          *dsn `yaml:"test-dsn"`       // 测试环境数据库配置
	MysqlConnTimeOut int  `yaml:"conn-time-out"`  // 数据库连接超时时间，单位秒
	QueryTimeOut     int  `yaml:"query-time-out"` // 数据库SQL执行超时时间，单位秒

	// +++++++++++++++日志相关+++++++++++++++++
	// 日志级别，这里使用了 beego 的 log 包
	// [0:Emergency, 1:Alert, 2:Critical, 3:Error, 4:Warning, 5:Notice, 6:Informational, 7:Debug]
	LogLevel int `yaml:"log-level"`
	// 日志输出位置，默认日志输出到控制台
	// 目前只支持['console', 'file']两种形式，如非console形式这里需要指定文件的路径，可以是相对路径
	LogOutput string `yaml:"log-output"`
	// 日志最大保留天数
	LogMaxDays  int `yaml:"log-maxdays"`
	LogChanSize int `yaml:"log-chan-size"`
	// +++++++++++++++日志相关结束+++++++++++++++++

	// +++++++++++++++grpc消息传递相关+++++++++++++++++
	RpcServer             *rpcdsn             `yaml:"rpc-server"`               //rpc server配置
	AgentTimeOut          int                 `yaml:"agent-timeout"`            //客户端rpc交互channel超时时间,毫秒
	AgentPort             int                 `yaml:"agent-port"`               //客户端rpc交互端口
	AgentMsgChanBuffer    int                 `yaml:"agent-msg-chan-buffer"`    //rpc server初始化agentMsg消息队列缓冲区大小
	FuncRetryCount        int                 `yaml:"func-retry-count"`         //rpc函数正常重试次数
	FuncMaxRetryCount     int                 `yaml:"func-max-retry-count"`     //rpc函数最大重试次数
	FuncRetryIntervalTime int                 `yaml:"func-retry-interval-time"` //rpc函数最大重试次数
	ServerRpcKeepalive    *serverrpckeepalive `yaml:"server-rpc-keepalive"`     //agent-rpc-keepalive配置
	TcpConnTimeOut        int                 `yaml:"connect-timeout"`          //tcp建连接的超时时间
	// +++++++++++++++定时任务相关+++++++++++++++++
	HeartbeatCronStep                int   `yaml:"heartbeat-cron-step"`                  // 实例心跳监控时间间隔
	HeartbeatTimeout                 int   `yaml:"heartbeat-timeout"`                    // 实例心跳超时时间差
	MdcServerMonitorCronStep         int   `yaml:"mdc-server-monitor-cron-step"`         // MS心跳监控时间间隔
	MdcServerMonitorCronTimeout 	 int   `yaml:"mdc-server-monitor-cron-timeout"`		 //	等待上次备份任务完成的最大超时时间
	MdcServerMonitorHeartbeatTimeout int   `yaml:"mdc-server-monitor-heartbeat-timeout"` // MS心跳超时时间差
	MdcServerId                      int64 `yaml:"mdc-server-id"`                        // MS_id
	BinlogProcessDetect              int   `yaml:"binlog-process-detect"`                // binlog定时探测间隔
	CleanDataStep                    int   `yaml:"clean_data_step"`                      // 定时清理间隔
	RemoteDataSaveDays               int   `yaml:"remote_data_save_days"`                // 大磁盘机器保留天数
	DbaBosSaveDays                   int   `yaml:"dba_bos_save_days"`                    // dba bos数据保留天数
	SiodBosSaveDays                  int   `yaml:"siod_bos_save_days"`                   // siod bos数据保留天数
	RetryUploadSIODStep              int   `yaml:"retry_upload_siod_step"`               //重试发起siod上传任务

	// bigdisk-hit-num
	BigDiskHitNum int `yaml:"remote—instance-hit-num"` //一台大磁盘一天最多承接任务数

	// +++++++++++++++dao相关+++++++++++++++++
	RegisterDatabase string `yaml:"register-database"`
	MaxIdleConns     int    `yaml:"max-idle-conns"`
	MaxOpenConns     int    `yaml:"max-open-conns"`
	OrmDebugSwitch   bool   `yaml:"orm-debug-switch"`
	// +++++++++++++++worker相关+++++++++++++++++
	WorkerNumber      int `yaml:"worker-number"`
	WorkerChanTimeOut int `yaml:"worker-chan-timeout"`

	// +++++++++++++++bos配置相关+++++++++++++++++
	DbaBosBucket  string `yaml:"dba_bos_bucket"`
	SiodBosBucket string `yaml:"siod_bos_bucket"`
	// +++++++++++++++邮件发送配置相关+++++++++++++++++
	SmtpServer       string `yaml:"smtp_server"`
	EmailSender      string `yaml:"email_sender"`
	EmailDevReciver  string `yaml:"email_dev_reciver"`
	CopyRequestBody  bool   `yaml:"copy_request_body"`
	TokenSignKey     string `yaml:"token_sign_key"`
	DxmUuapUicAppKey string `yaml:"dxm_uuap_uic_app_key"`
	DxmUuapUicSecret string `yaml:"dxm_uuap_uic_secret"`
	DxmUuapUicUrl    string `yaml:"dxm_uuap_uic_url"`
	//SIOD灾备接口相关
	SiodBackupManualRun            string `yaml:"siod_backup_manual_run"`
	SiodBackupAutoRegisterProtocol string `yaml:"siod_backup_auto_register_protocol"`
	SiodRegisterToken              string `yaml:"siod_register_token"`
	SiodBackupPlatform             string `yaml:"siod_backup_platform"`
}

//keep alive配置
type keepaliveclientparam struct {
	Time                int  `yaml:"time"`
	TimeOut             int  `yaml:"time-out"`
	PermitWithOutStream bool `yaml:"permit-without-stream"`
}

type keepaliveserverparam struct {
	MaxConnectionIdle     int `yaml:"max-connection-idle"`
	MaxConnectionAge      int `yaml:"max-connection-age"`
	MaxConnectionAgeGrace int `yaml:"max-connection-age-grace"`
	Time                  int `yaml:"time"`
	Timeout               int `yaml:"time-out"`
}

type keepaliveenforcementpolicy struct {
	MinimumTime         int  `yaml:"minimum-time"`
	PermitWithoutStream bool `yaml:"permit-without-stream"`
}

type serverrpckeepalive struct {
	ClientParam       *keepaliveclientparam       `yaml:"client-param"`
	ServerParam       *keepaliveserverparam       `yaml:"server-param"`
	EnforcementPolicy *keepaliveenforcementpolicy `yaml:"enforcement-policy"`
}
type rpcdsn struct {
	Addr              string `yaml:"addr"`
	Port              int    `yaml:"port"`
	Hostname          string `yaml:"hostname"`
	ConnectionTimeout int    `yaml:"connection-timeout"`
}
type dsn struct {
	MdcDbAddr           string `yaml:"mdc_db_addr"`
	MdcDbSchema         string `yaml:"mdc_db_schema"`
	MdcDbUser           string `yaml:"mdc_db_user"`
	MdcDbPassword       string `yaml:"mdc_db_password"`
	TinkerDbAddr        string `yaml:"tinker_db_addr"`
	TinkerDbSchema      string `yaml:"tinker_db_schema"`
	TinkerDbUser        string `yaml:"tinker_db_user"`
	TinkerDbPassword    string `yaml:"tinker_db_password"`
	FdbTinkerDbAddr     string `yaml:"fdb_tinker_db_addr"`
	FdbTinkerDbSchema   string `yaml:"fdb_tinker_db_schema"`
	FdbTinkerDbUser     string `yaml:"fdb_tinker_db_user"`
	FdbTinkerDbPassword string `yaml:"fdb_tinker_db_password"`
	Charset             string `yaml:"charset"`
	Disable             bool   `yaml:"disable"`
	//版本自动检查，不可配置
	Version int `yaml:"-"`
}

var Config = &Configuration{
	OnlineDSN: &dsn{
		MdcDbSchema: "information_schema",
		Charset:     "utf8mb4",
		Disable:     true,
		Version:     99999,
	},
	TestDSN: &dsn{
		MdcDbSchema: "information_schema",
		Charset:     "utf8mb4",
		Disable:     true,
		Version:     99999,
	},

	//agentMsgChan的缓冲大小
	AgentMsgChanBuffer: 1000,
	//rpc函数错误重试相关
	FuncRetryCount:        3,
	FuncMaxRetryCount:     5,
	FuncRetryIntervalTime: 1000,
	//客户端rpc交互channel超时时间,毫秒
	AgentTimeOut: 180000,
	AgentPort:    10006,
	//rpc server配置
	RpcServer: &rpcdsn{
		Addr:     "127.0.0.1",
		Port:     10000,
		Hostname: "testhost",
		//http连接创建时握手交互超时时间
		ConnectionTimeout: 35,
	},
	//keepalive配置
	//一个连接可被多次rpc交互复用，以下配置决定这个连接什么时候断开
	ServerRpcKeepalive: &serverrpckeepalive{
		ClientParam: &keepaliveclientparam{
			//如果在该时间内，client未发现活跃连接，则对server发送ping包探活
			//最小设置10s，小于10s则自动设置为1分钟
			Time: 120,
			//从keepalive ping包发起时间点开始timeout时间内如果未发生rpc交互，则判定连接为关闭状态
			TimeOut: 35,
			//如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
			//如果设置为false，如果没有活跃的rpc请求，则不做连接探活 =>出于资源消耗考虑选false
			PermitWithOutStream: false,
		},
		ServerParam: &keepaliveserverparam{
			//连接最大空闲时长，超过则关闭连接
			MaxConnectionIdle: 30,
			//连接最大保持时长，这个值会上下浮动10%后进行传播
			//官方原文：connection may exist before it will be closed by sending a GoAway.
			// Arandom jitter of +/-10% will be added to MaxConnectionAge to spread out
			// connection storms.
			MaxConnectionAge: math.MaxInt64,
			//链接达到MaxConnectionAge的时间后，会在MaxConnectionAgeGrace范围内被强制关闭
			MaxConnectionAgeGrace: math.MaxInt64,
			//如果在该时间内，server未发现活跃连接，则对client发送ping包探活
			//最小设置10s，小于10s则自动设置为1分钟
			Time: 120,
			//client端ping超时等待时间，即使没有活跃连接或连接已经关闭，也依然等待这个时间走完。
			Timeout: 35,
		},
		EnforcementPolicy: &keepaliveenforcementpolicy{
			//client端发出探活包的最小等待时间
			MinimumTime: 100,
			//如果设置为true，即使没有活跃的rpc请求，也依然做连接探活
			//如果设置为false，如果没有活跃的rpc请求，client端发过来
			// ping包后server会返回连接关闭标志并关闭连接 =>出于资源消耗考虑选false
			PermitWithoutStream: false,
		},
	},
	TcpConnTimeOut:   5000,
	MysqlConnTimeOut: 3,
	QueryTimeOut:     30,
	LogLevel:         3,
	LogOutput:        "mdc-server.log",
	LogMaxDays:       30,
	LogChanSize:      10,

	HeartbeatCronStep:                10,
	HeartbeatTimeout:                 30,
	MdcServerMonitorCronStep:         10,
	MdcServerMonitorCronTimeout:      12,
	MdcServerMonitorHeartbeatTimeout: 120,
	RegisterDatabase:                 "mysql",
	MaxIdleConns:                     10,
	MaxOpenConns:                     200,
	OrmDebugSwitch:                   false,
	WorkerNumber:                     60, //Worker协程数
	WorkerChanTimeOut:                10, //Worker单个协程空闲超时时间
	BinlogProcessDetect:              600,
	CleanDataStep:                    3600,
	DbaBosBucket:                     "dxm-dbbk",
	SiodBosBucket:                    "dxm-siod-backup-hn",
	SmtpServer:                       "mail-out.sys.baidu.com:25",
	EmailSender:                      "<EMAIL>",
	EmailDevReciver:                  "genggangfeng_dxm",
	CopyRequestBody:                  true,
	TokenSignKey:                     "siod-backends",
	DxmUuapUicAppKey:                 "uuapclient-10-7dthLVzsgL96hwtaeyHK",
	DxmUuapUicSecret:                 "75bce4e58dbf49679f093c",
	DxmUuapUicUrl:                    "https://uuap.duxiaoman-int.com/uic",
	SiodBackupPlatform:               "mdc",
	SiodBackupManualRun:              "http://sc.duxiaoman-int.com/backup-recovery-engine/api/v1/task/manualRun",
	SiodRegisterToken:                "39f1af1f141340618161e6cf33fbd7ad",
	SiodBackupAutoRegisterProtocol:   "http://sc.duxiaoman-int.com/backup-recovery-engine/api/v1/protocol/autoRegisterProtocol",
}

// 加载配置文件
func (conf *Configuration) readConfigFile(path string) error {
	configFile, err := os.Open(path)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) os.Open failed: %v", path, err))
		return err
	}
	defer configFile.Close()
	content, err := ioutil.ReadAll(configFile)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) ioutil.ReadAll failed: %v", path, err))
		return err
	}
	err = yaml.Unmarshal(content, Config)
	if err != nil {
		os.Stderr.WriteString(fmt.Sprintf("readConfigFile(%s) yaml.Unmarshal failed: %v", path, err))
	}
	return err
}

// 配置初始化
func ParseConfig(configFile string) error {
	var err error
	// 如果未传入配置文件，则返回报错
	if "" == configFile {
		return errors.New("No config file input")
	}
	// 配置文件状态检查
	if _, err = os.Stat(configFile); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-server start failed] Check config file failed."+
			" err=%v ConfFile=%v \n", time.Now().Format(TIME_FORMAT), err, configFile))
		return err
	}
	// 配置文件解析
	if err = Config.readConfigFile(configFile); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc-server start failed]"+
			" Parse config file failed. ConfFile=%v err=%v \n", time.Now().Format(TIME_FORMAT), err, configFile))
		return err
	}
	return LoggerInit()
}
