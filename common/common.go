package common

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
)

//发起备份时的入参
type SiodBkRequest struct {
	Platform    string `json:"platform"`
	Token       string `json:"token"`
	ProtocolID  int64  `json:"protocolID"`
	ProductName string `json:"productName"`
	AppName     string `json:"appName"`
	DataVersion string `json:"dataVersion"`
}

//Siod返回的参数结构
type SiodPlatResult struct {
	Errno  int         `json:"errno"`
	Errmsg string      `json:"errmsg"`
	Data   interface{} `json:"data"`
}

//注册siod bos灾备的接口
type SiodRegister struct {
	Platform string           `json:"platform"`
	Token    string           `json:"token"`
	Conf     SiodRegisterConf `json:"conf"`
}

type SiodRegisterConf struct {
	CreateType        int    `json:"createType"`
	Product           string `json:"product"`
	Subsystem         string `json:"subsystem"`
	AppName           string `json:"appName"`
	DataType          string `json:"dataType"`
	SourceDataType    int    `json:"sourceDataType"`
	BackupInstanceNum int    `json:"backupInstanceNum"`
	SourceDataPath    string `json:"sourceDataPath"`
	ExcludeDirectory  string `json:"excludeDirectory"`
	BackupUser        string `json:"backupUser"`
	RunningType       int    `json:"runningType"`
	OpOwner           string `json:"opOwner"`
	RdOwner           string `json:"rdOwner"`
	NeedTape          int    `json:"needTape"`
	RetentionTime     int    `json:"retentionTime"`
}

//POST函数
func HTTPPost(url string, bodys io.Reader) (resp SiodPlatResult, err error) {
	var (
		respBody       *http.Response
		siodPlatResult SiodPlatResult
		body           []byte
	)
	//发请求
	if respBody, err = http.Post(url, "application/json", bodys); err != nil {
		Log.Warning("request failed, url=[%v], bodys=[%+v], err=[%v]", url, bodys, err)
		return siodPlatResult, err
	}
	defer respBody.Body.Close()
	//解析请求
	if body, err = ioutil.ReadAll(respBody.Body); err != nil {
		Log.Warning("prase request body failed, url=[%v], bodys=[%+v], err=[%v]", url, bodys, err)
		return siodPlatResult, err
	}
	if err = json.Unmarshal(body, &siodPlatResult); err != nil {
		Log.Warning("Unmarshal body failed, url=[%v], body=[%+v],string(body)=[%v], err=[%v]", url, bodys, string(body), err)
		return siodPlatResult, err
	}
	return siodPlatResult, nil
}

//获得responce data的值
func GetRespDataVal(data interface{}) (err error, value int64) {
	switch val := data.(type) {
	case int:
		value = int64(val)
	case int64:
		value = val
	case int32:
		value = int64(val)
	case float64:
		value = int64(val)
	default:
		errMsg := fmt.Sprintf("Get responce data failed,data=[%v],type=[%T]", data, data)
		Log.Warning(errMsg)
		return errors.New(errMsg), -1
	}
	return nil, value
}

// 获取上传SIOD数据版本号
func GetDataVersion(dbaBosPath string) string {
	//例如:umoneypay3_766834243/20220420/inc6
	//>= 2避免数据库存储空值
	var dataVersion string

	if bkBostFileStr := strings.Split(dbaBosPath, "/"); len(bkBostFileStr) >= 2 {
		dataVersion = bkBostFileStr[1]
	}

	return dataVersion
}
