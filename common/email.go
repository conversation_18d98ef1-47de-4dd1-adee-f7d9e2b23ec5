package common

import (
	"crypto/sha256"
	"dxm/siod-cloud/go-common-lib/olog"
	"encoding/json"
	"fmt"
	"github.com/jordan-wright/email"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"sort"
	"strconv"
	"strings"
	"time"
)

var (
	smtpServer       string
	emailSender      string
	emailDevReceiver string // 开发环境测试环境 邮件接收人
	env              string
)

var (
	APPKey      string
	SecretKey   string
	UICURL      string
	bolInitTest bool
	UICVersion  string
)

type ResultInfo struct {
	Code   int                    `json:"code"`
	Desc   string                 `json:"desc"`
	Result map[string]interface{} `json:"result"`
}

func InitEmail() {
	Log.Info("ready to init messageSender\n")
	smtpServer = Config.SmtpServer
	if len(strings.Trim(smtpServer, "")) == 0 {
		smtpServer = "mail-out.sys.baidu.com:25"
	}
	Log.Debug("SMTP_SERVER=(%+v)", smtpServer)

	emailSender = Config.EmailSender
	if len(strings.Trim(emailSender, "")) == 0 {
		emailSender = "<EMAIL>"
	}
	Log.Debug("EMAIL_SENDER=(%+v)", emailSender)
	emailDevReceiver = Config.EmailDevReciver
	if len(strings.Trim(emailDevReceiver, "")) == 0 && env != "prod" {
		// olog.Info("检查message_sender配置文件")
		olog.Info("dev or test env must config emailDevReceiver")
		time.Sleep(1 * time.Second)
		os.Exit(1)
	}
	Log.Debug("EMAIL_DEV_RECEIVER=(%+v)", emailDevReceiver)
	Log.Info("finish to init messaeSender\n")
}

func Sendmail(subject, content string, receivers, cc, attach_files []string) {
	Log.Warn("receivers=[%v]", receivers)
	//Get email by receivers
	emailList := []string{}
	for _, userName := range receivers {
		userInfo := GetUserInfoByUserName(userName, "email")
		if userInfo.Code == 200 {
			userEmail, isOK := userInfo.Result["email"].(string)
			if isOK {
				emailList = append(emailList, userEmail)
			} else {
				Log.Error("userName:%s is not a validate uuap name ", userName)
			}
		} else {
			continue
		}
	}
	Log.Warn("receiverList:%+v", emailList)
	e := email.NewEmail()
	e.From = emailSender
	e.To = emailList
	if cc != nil {
		// e.Cc = cc
	}
	e.Subject = subject
	e.HTML = []byte(content)

	if attach_files != nil {
		for _, one := range attach_files {
			e.AttachFile(one)
		}
	}

	err := e.Send(smtpServer, nil)
	if err != nil {
		Log.Error(fmt.Sprintf("send mail failed ,err=[%v]", err))
		Log.Info("Send email error from sender:%+v to receiver:%+v", e.From, e.To)
	}
}

func InitUuap() {
	Log.Info("ready to init uuap")
	SecretKey = Config.DxmUuapUicSecret
	if SecretKey == "" {
		Log.Warn("Fail to get dxm uuap SecretKey.")
		goto failOut
	}
	Log.Debug("SecretKey=(%+v)", SecretKey)

	APPKey = Config.DxmUuapUicAppKey
	if APPKey == "" {
		Log.Warn("Fail to get dxm uuap uuap.appKey.")
		goto failOut
	}
	Log.Debug("APPKey=(%+v)", APPKey)

	// 获取uic url
	UICURL = Config.DxmUuapUicUrl
	if UICURL == "" {
		Log.Warn("Fail to get dxm uuap UICURL.")
		goto failOut
	}
	Log.Debug("UICURL=(%+v)", UICURL)
	Log.Debug("finish to init uuap-old")
	return

failOut:
	Log.Warn("Fail to init uuap-dxm.")
}

// GetUserInfoByUserName 根据用户名获取用户相关信息
func GetUserInfoByUserName(username string, fields string) (info ResultInfo) {
	var returnFields string
	if fields == "" {
		returnFields = "name,email,organizationId,organizationCode,organizationName,passportUsername"
	} else {
		returnFields = fields
	}
	var data = map[string]interface{}{
		"returnFields": returnFields,
		"username":     username,
	}
	UICBase("/user/getUserByUsername", data, &info)
	return
}

//  sha256Sign 加密签名
func sha256Sign(paramsStr string) (signStr string) {
	h := sha256.New()
	h.Write([]byte(paramsStr))
	signStr = fmt.Sprintf("%x", h.Sum(nil))
	return
}

// sortValueByKey 按照字典的key 来排序value
func sortValueByKey(params map[string]interface{}) (sortedValue string) {
	var keys []string
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, k := range keys {
		switch params[k].(type) {
		case string:
			sortedValue += params[k].(string)
			break
		case int:
			sortedValue += strconv.Itoa(params[k].(int))
			break
		case int64:
			sortedValue += strconv.FormatInt(params[k].(int64), 10)
			break
		}
	}
	return
}

// dataToUrlPath map 转为url
func dataToUrlPath(data map[string]interface{}) (urlPath string) {
	// var paramsList []string
	newValue := url.Values{}

	for k, v := range data {
		switch v.(type) {
		case string:
			newValue.Add(k, fmt.Sprintf("%s", v))
			break
		case int, int64:
			newValue.Add(k, fmt.Sprintf("%d", v))
			break
		}
	}

	urlPath = newValue.Encode()
	return
}

// UICBase 获取UIC 信息 返回结构体
func UICBase(api string, input map[string]interface{}, info interface{}) {
	url := UICURL + UICVersion + api

	data := map[string]interface{}{
		"appKey":    APPKey,
		"timestamp": strconv.FormatInt(time.Now().Unix(), 10),
	}
	for key, value := range input {
		data[key] = value
	}
	sign := sha256Sign(sortValueByKey(data) + SecretKey)
	data["sign"] = sign
	resp, err := http.Post(url, "application/x-www-form-urlencoded",
		strings.NewReader(dataToUrlPath(data)))

	if err == nil {
		defer resp.Body.Close()
		res, _ := ioutil.ReadAll(resp.Body)
		json.Unmarshal(res, info)
	}
}
