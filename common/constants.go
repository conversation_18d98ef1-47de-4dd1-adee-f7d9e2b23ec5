package common

import protobuf "dt-common/protobuf/mdc-server"

// MsRole indicates the cs role
type <PERSON><PERSON><PERSON> uint8

const (
	MS_MASTER MsRole = iota
	MS_SLAVE
)

// MsStatus indicates the Ms status
type MsStatus uint8

const (
	MS_NORMAL MsStatus = iota
	MS_ERROR
)

//机器类型
type MachineType int32

const (
	BACKUP MachineType = iota
	RESTORE
)

//备份任务状态
type BkTaskStatus uint8

const (
	BS_INIT BkTaskStatus = iota
	BS_ONLINE
	BS_OFFLINE
)

//binlog 任务状态
type BinlogTaskStatus uint8

const (
	BT_OFFLINE BinlogTaskStatus = iota
	BT_ONLINE
)

//binlog 备份下线serverId置为初始值
const BT_OFFLINE_SERVERID = 0

//备份类型
type BkType int32

const (
	FullHotBk BkType = iota
	IncHotBk
)

//备份字段
const (
	FullHotBkStr = "/base"
	IncHotBkStr  = "/inc"
	UnpackType   = "tar"
)

//恢复类型
type RsType int32

const (
	AnyTime     RsType = iota // 恢复到任意时间点
	Snapshot                  // 天级别恢复
	ParseBinlog               // 解析binlog
)

var BkDay_Value = map[string]int32{
	"Sunday":    0,
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
}

//调用Siod灾备平台相关参数
const (
	//备份相关参数
	SiodBackupProductName = "siod-mdc"
	//注册相关参数
	SiodRegisterCreateType        = 2
	SiodRegisterProduct           = "siod-mdc"
	SiodRegistersubSystem         = "dxm-dbbk-all"
	SiodRegisterDataType          = "data"
	SiodRegisterSourceDataType    = 2
	SiodRegisterBackupInstanceNum = 1
	SiodRegisterBackupUser        = "work"
	SiodRegisterRunningType       = 2
	SiodRegisterOpOwner           = "liangpengfei_dxm"
	SiodRegisterRdOwner           = "nanfangjian_dxm"
	SiodRegisterNeedType          = 1
	SiodRegisterRetentionTime     = 120
	AppPrefix                     = "dbbk"
)

//大磁盘机器存储目录
const (
	RemoteBaseDir    = "/md_raid0mysql/mysqlbackup00"
	RemoteBinlogDir  = "/md_raid0mysql/binlogBk"
	BkBinlogBaseDir  = "/home/<USER>"
	RsPrefixPathName = "/home/<USER>"
)

//恢复发起者角色
type RsRole int32

const (
	Rd RsRole = iota
	Dba
)

// 磁盘可使用空间水位线 1024G
const DISK_LOWEST_LEVEL = 1024

// AbnormalDBABOSArray  以下状态不可进行数据拉取
var AbnormalDBABOSArray = [...]int32{
	int32(protobuf.XtraTaskStatus_UploadDbaBosFail),
	int32(protobuf.XtraTaskStatus_XtraTaskAbandon)}

/*
  上传bos成功前的中间态
*/
var BackupIntermediateMap = map[protobuf.XtraTaskStatus]struct{}{
	// 初始化
	protobuf.XtraTaskStatus_InitStatus: {},
	//执行中
	protobuf.XtraTaskStatus_XtraExecuting: {},
	//执行成功
	protobuf.XtraTaskStatus_XtraExecSuccess: {},
	//打包成功
	protobuf.XtraTaskStatus_UnpackSuccess: {},
	//上传bos中
	protobuf.XtraTaskStatus_UploadDbaBos: {},
}

const (
	InitLsnPos   = "0"
	EmptyLsnPos  = ""
	XbstreamType = "xbstream"
)
