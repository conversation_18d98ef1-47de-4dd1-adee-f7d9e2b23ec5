package scheduler

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
	"mdc-server/data"
)

func init() {
	common.ParseConfig("../conf/mdc-server.yaml")
	data.InitDao()
}

func TestCreateCleanBkData(t *testing.T) {
	<PERSON>vey("createCleanBkData：成功.", t, func() {
		err := createCleanBkData()
		So(err, ShouldBeNil)
	})
}

func TestCreateCleanRsData(t *testing.T) {
	<PERSON>vey("createCleanRsData：成功.", t, func() {
		err := createCleanRsData()
		So(err, ShouldBeNil)
	})
}
