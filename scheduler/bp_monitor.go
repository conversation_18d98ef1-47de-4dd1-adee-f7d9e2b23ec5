package scheduler

import (
	"fmt"
	"sync"
	"time"

	pb_server "dt-common/protobuf/mdc-server"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

//binlog process 探测结构体
type bpMonitorTimer struct {
	Tick     *time.Ticker
	StopChan chan bool
}

var bpMonitorTimerController *bpMonitorTimer

//启动binlog Process探测协程
func (h *bpMonitorTimer) Start(wg *sync.WaitGroup) {
	h.Tick = time.NewTicker(time.Second * time.Duration(common.Config.BinlogProcessDetect))
	h.<PERSON><PERSON><PERSON> = make(chan bool)
	common.Log.Notice("Start scaning bpMonitorTimer... ")
	for {
		select {
		case <-h.Tick.C:
			binlogProcessDetect()
		case stop := <-h.Stop<PERSON>han:
			if stop {
				common.Log.Notice("Receive exit signal. Stop scaning bpMonitorTimer.")
				wg.Done()
				return
			}
		}
	}
}

//关闭binlog Process探测协程
func (h *bpMonitorTimer) Stop() {
	h.Tick.Stop()
	h.<PERSON><PERSON>han <- true
	close(h.<PERSON><PERSON><PERSON>)
}

//binlog备份进程探测函数入口
func binlogProcessDetect() {
	//ms角色认证，由master ms进行定时探测binlog进程
	role, err := data.CheckRole(common.Config.MdcServerId)
	if err != nil {
		common.Log.Warn("check ms role failed, err=[%v]", err)
		return
	}
	//对ms身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		return
	case uint8(common.MS_MASTER):
		//创建binlog探测任务并放入消息队列
		if err = createBinlogDetectTask(); err != nil {
			common.Log.Warn("createBinlogDetectTask failed,err=[%v]", err)
		}
	default:
		common.Log.Warn("unknown ms role=[%v]", role)
	}
	return
}

//创建binlog进程探测任务
func createBinlogDetectTask() error {
	//查询获得开启binlog进程备份的备份集群信息
	err, bkTaskInfo := data.GetBinlogTaskOnlineList()
	if len(bkTaskInfo) <= 0 || err != nil {
		common.Log.Info("BinlogTaskOnlineTask is empty")
		return err
	}
	for _, onlineTask := range bkTaskInfo {
		//构建binlog探测请求发送到消息队列
		startBkMsg := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_BINLOG_DETECT,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   onlineTask.ClusterId,
				ClusterName: onlineTask.ClusterName,
				NodeId:      onlineTask.NodeId,
			},
			MsgType: &pb_server.XwebPlusFrontAsyncMsg_BinlogDetectTaskReqMsg{
				BinlogDetectTaskReqMsg: &pb_server.BinlogDetectTaskReqMsg{
					ServerId: onlineTask.BinlogServerId,
				},
			},
		}
		err = communicator.PutMsMsgToChan(*startBkMsg)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to put msg to msgchan,clusterId=[%v],clusterName=[%v],err=[%v]", onlineTask.ClusterId, onlineTask.ClusterName, err)
			common.Log.Error("[Mdc-Server] ErrorMsg=[%v] CalledError=[%v]", common.ErrMsMsgToChannel, errMsg)
			return err
		}
	}
	return nil
}
