package scheduler

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestGeneSpec(t *testing.T) {
	<PERSON>vey("GeneSpec：成功.", t, func() {
		spec := GeneSpec("02:34:56")
		So(spec, ShouldNotBeNil)

		spec = GeneSpec("21:34:56")
		So(spec, ShouldNotBeNil)
	})

	<PERSON>vey("GeneSpec：失败.", t, func() {
		spec := GeneSpec("")
		So(spec, ShouldEqual, "")
	})
}

func TestAddCronTask(t *testing.T) {
	<PERSON>vey("AddCronTask：成功.", t, func() {
		err := AddCronTask("02:34:56", 1, 0)
		So(err, ShouldBeNil)
	})
}

func TestDelCronTask(t *testing.T) {
	<PERSON>vey("DelCronTask：成功.", t, func() {
		err := AddCronTask("02:34:56", 1, 0)
		So(err, ShouldBeNil)

		err = DelCronTask(1, 0)
		So(err, ShouldBeNil)
		So(len(ClusterXtraManage.Cron.Entries()), ShouldEqual, 0)
	})
}
