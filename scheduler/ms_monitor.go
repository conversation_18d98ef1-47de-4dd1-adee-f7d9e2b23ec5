package scheduler

import (
	"errors"
	"fmt"
	"net"
	"strings"
	"sync"
	"time"

	dao_mdc "dt-common/dao/mdc"

	"mdc-server/common"
	"mdc-server/data"
)

//mdc-server 探测结构
type msMonitorTimer struct {
	Tick     *time.Ticker
	StopChan chan bool
}

var msMonitorTimerController *msMonitorTimer

//启动msMonitor检测协程
func (h *msMonitorTimer) Start(wg *sync.WaitGroup) {
	h.Tick = time.NewTicker(time.Second * time.Duration(common.Config.MdcServerMonitorCronStep))
	h.<PERSON><PERSON>han = make(chan bool)
	common.Log.Notice("Start scaning msMonitor... ")
	for {
		select {
		case <-h.Tick.C:
			err := msHealthCheck(common.Config.MdcServerId)
			if err != nil {
				common.Log.Warn("ms healthCheck failed! reason = [%v]", err)
			}
		case stop := <-h.<PERSON><PERSON>han:
			if stop {
				common.Log.Notice("Receive exit signal. Stop scaning msMonitor.")
				wg.Done()
				return
			}
		}
	}
}

//关闭msMonitor检测协程
func (h *msMonitorTimer) Stop() {
	h.Tick.Stop()
	h.StopChan <- true
	close(h.StopChan)
}

//获取本机IP
func GetLocalIp() (ip string, err error) {
	netInterfaces, err := net.Interfaces()
	if err != nil {
		common.Log.Warn("Fail to execute net.Interfaces(),reason=[%v]", err)
		return "", err
	}
	for i := 0; i < len(netInterfaces); i++ {
		if (netInterfaces[i].Flags & net.FlagUp) != 0 {
			addrs, _ := netInterfaces[i].Addrs()
			for _, address := range addrs {
				if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						ip = ipnet.IP.String()
						return ip, nil
					}
				}
			}
		}
	}
	common.Log.Warn("No network IP available")
	err = errors.New("No IP obtained")
	return "", err
}

//向DB中注册Ms信息
func MsRegister(serverId int64) error {
	//获取本机IP
	ip, err := GetLocalIp()
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetLocalIp, err)
		return err
	}
	//去除ip首尾的空格以及换行符
	err = data.InitMsInfo(serverId, strings.Trim(ip, " \t\r\n"))
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrRegisterMs, err)
		return err
	}
	return nil
}

// mdc-server健康检查入口
func msHealthCheck(serverId int64) error {
	/*
	 * 1.从ms_monitor表中获取ms实例信息
	 * 2.ms实例状态为异常，则说明前几次的周期检查过程中和db不能正常通信；现在通信恢复了，进行状态修复
	 * 3.返回ms实例的角色信息
	 */
	role, err := data.CheckRole(serverId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrGetMsInfo, err)
		return err
	}
	//更新ms_monitor表中ms实例的心跳时间戳
	err = registerHeartbeatToDb(serverId)
	if err != nil {
		common.Log.Warn("ErrorMsg=[%v] CalledError=[%v]", common.ErrRegisterMsMonitor, err)
		return err
	}
	//对ms身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		err = monitorMasterTimerStatus()
		if err != nil {
			common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],tinkerId=[%v]", common.ErrMonitorMsMaster, err, serverId)
			return err
		}
	case uint8(common.MS_MASTER):
		err = monitorSlaveTimerStatus()
		if err != nil {
			common.Log.Warn("ErrorMsg=[%v] CalledError=[%v],tinkerId=[%v]", common.ErrMonitorMsSlave, err, serverId)
			return err
		}
	default:
		errMsg := fmt.Sprintf("ErrorMsg=[%v],role=[%v]", common.ErrAbnormalMsRole, common.MsRole(role))
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//向数据库中注册ms的心跳信息
func registerHeartbeatToDb(serverId int64) error {
	//获取当前时间戳
	currentTime := time.Now().UnixNano()
	cond := []string{"MdcServerId"}
	cols := []string{"HeartBeat"}
	msMonitor := dao_mdc.MsMonitor{MdcServerId: serverId, HeartBeat: currentTime}
	//更新ms心跳信息
	msCnt, err := msMonitor.UpdateByCondCols(cond, cols)
	if err != nil {
		common.Log.Warn("Failed to update ms heartbeat reason=[%v]", err)
	} else if msCnt != 1 {
		errMsg := fmt.Sprintf("Can't find MsInfo, register Heartbeat failed, serverId=[%v]", serverId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return err
}

//Master Timer监控Slave Timer的心跳信息
func monitorSlaveTimerStatus() error {
	//获取当前时间戳
	currentTime := time.Now().UnixNano()
	//超时时间
	timeOut := int64(common.Config.MdcServerMonitorHeartbeatTimeout) * 1e9
	//获取状态正常的Slave Timer信息
	cols := []string{"Role", "Status"}
	msSlave := dao_mdc.MsMonitor{Role: uint8(common.MS_SLAVE), Status: uint8(common.MS_NORMAL)}
	msList, err := msSlave.ReadMsMonitorByCols(cols)
	if err != nil {
		common.Log.Warn("Failed to get ms info reason=[%v]", err)
		return err
	}
	//遍历所有的slaveTimer对心跳进行检测
	for _, msInfo := range msList {
		//心跳检测
		if !isNodeAlive(msInfo.HeartBeat, currentTime, timeOut) {
			//处理异常slaveTimer的过程封装成一个事务
			//开启事务
			ptrOrmer, beginTrxErr := data.BeginTrx()
			if beginTrxErr != nil {
				common.Log.Warn("Start transaction failed! reason=[%v]", beginTrxErr)
				continue
			}
			err = data.GetSlaveTimerLock(ptrOrmer, msInfo.MdcServerId)
			if err != nil {
				common.Log.Warn("Failed to get slaveTimerLock,reason=[%v]", err)
				data.RollbackTrx(ptrOrmer)
				continue
			}
			//持锁成功后进行任务的继承操作
			err = data.InheritSlaveTimerRole(msInfo.MdcServerId, ptrOrmer)
			if err != nil {
				common.Log.Warn("Failed to inherit slaveTimer task,reason=[%v]", err)
				data.RollbackTrx(ptrOrmer)
				continue
			}
			commitErr := data.CommitTrx(ptrOrmer)
			if commitErr != nil {
				common.Log.Warn("Inherit Task failed due to transaction commit failure! reason=[%v]", commitErr)
				continue
			}

			// 只有DB层继承操作提交完成,启动定时timer
			common.Waitgroup.Add(3)
			go Start(common.Waitgroup)
			//只有DB层继承操作提交完成,初始化定时热备任务
			common.Waitgroup.Add(1)
			go StartInitCron(common.Waitgroup)
		}
	}
	return nil
}

//Slave Timer监控Master Timer的心跳信息
func monitorMasterTimerStatus() error {
	//获取当前时间戳
	currentTime := time.Now().UnixNano()
	//超时时间
	timeOut := int64(common.Config.MdcServerMonitorHeartbeatTimeout) * 1e9
	//获取Master Timer信息
	cols := []string{"Role", "Status"}
	msMaster := dao_mdc.MsMonitor{Role: uint8(common.MS_MASTER), Status: uint8(common.MS_NORMAL)}
	msList, err := msMaster.ReadMsMonitorByCols(cols)
	if err != nil {
		common.Log.Warn("Failed to get ms info reason=[%v]", err)
		return err
	} else if len(msList) == 0 {
		//无master ms将自己变更为Master ms
		serverId := common.Config.MdcServerId
		err := data.SetMsToMaster(serverId)
		if err != nil {
			common.Log.Warn("Failed to set msRole to Master,reason=[%v]", err)
			return err
		}
		return nil
	}
	//判断master timer心跳是否异常
	if !isNodeAlive(msList[0].HeartBeat, currentTime, timeOut) {
		//处理异常masterTimer的过程封装成一个事务
		//开启事务
		ptrOrmer, beginTrxErr := data.BeginTrx()
		if beginTrxErr != nil {
			common.Log.Warn("Start transaction failed! reason=[%v]", beginTrxErr)
			return beginTrxErr
		}
		//master Timer 角色的竞争以及old Master Timer可重入任务的继承工作
		oldServerId, err := data.GetMasterTimerLock(ptrOrmer)
		if err != nil {
			common.Log.Warn("Failed to get MasterLock reason=[%v]", err)
			data.RollbackTrx(ptrOrmer)
			return err
		}
		//当持有old masterTimer写锁后，对其心跳进行二次判断
		err = msList[0].ReadByIndexs()
		if err != nil {
			//二次判断出错不影响整体任务继续执行，仅打印错误日志
			common.Log.Warn("The second judgment failed, reason=[%v]", err)
		}
		//二次判断中如果发现old masterTimer心跳恢复正常则退出
		if isNodeAlive(msList[0].HeartBeat, currentTime, timeOut) {
			errMsg := fmt.Sprintf("The oldMasterTimer=[%v] found in the second judgment returns to normal,", msList[0].MdcServerId)
			common.Log.Notice(errMsg)
			data.RollbackTrx(ptrOrmer)
			return nil
		}
		//持有masterTimer的写锁进行后续继承操作
		err = data.InheritMasterTimerRole(common.Config.MdcServerId, oldServerId, ptrOrmer)
		if err != nil {
			common.Log.Warn("Failed to inherit MasterTimer role,reason=[%v]", err)
			data.RollbackTrx(ptrOrmer)
			return err
		}
		commitErr := data.CommitTrx(ptrOrmer)
		if commitErr != nil {
			common.Log.Warn("Inherit Task failed due to transaction commit failure! reason=[%v]", commitErr)
			return commitErr
		}
		// 只有DB层继承操作提交完成,启动定时timer
		common.Waitgroup.Add(3)
		go Start(common.Waitgroup)
		//只有DB层继承操作提交完成,初始化定时热备任务
		common.Waitgroup.Add(1)
		go StartInitCron(common.Waitgroup)
	}
	return nil
}

//判断ms心跳是否正常，判断逻辑: 当前时间戳与当前ms心跳差值大于自定义的heartbeatTimeout则为false
func isNodeAlive(heartbeat int64, currentTime int64, heartbeatTimeout int64) bool {
	//判断心跳是否超时
	if currentTime-heartbeat > heartbeatTimeout {
		return false
	}
	//判断心跳不超时,返回true
	return true
}
