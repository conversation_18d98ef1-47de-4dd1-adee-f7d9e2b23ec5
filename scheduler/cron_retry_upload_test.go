package scheduler

import (
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	dao_mdc "dt-common/dao/mdc"
)

func TestMakeNoahAppName(t *testing.T) {
	Convey("单机", t, func() {
		taskInfo := &dao_mdc.BkTaskInfo{
			NodeId:      0,
			ClusterName: "test1",
			NodeName:    "",
		}
		noahApp := makeNoahAppName(taskInfo)
		fmt.Println(noahApp)
		So(noahApp, ShouldEqual, "dbbk-test1")
	})

	Convey("DDBS", t, func() {
		taskInfo := &dao_mdc.BkTaskInfo{
			NodeId:      111,
			ClusterName: "test1",
			NodeName:    "node1",
		}
		noahApp := makeNoahAppName(taskInfo)
		fmt.Println(noahApp)
		So(noahApp, ShouldEqual, "dbbk-test1-node1")
	})
}
