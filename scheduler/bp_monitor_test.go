package scheduler

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func TestCreateBinlogDetectTask(t *testing.T) {
	Convey("createBinlogDetectTask：成功.", t, func() {
		err := createBinlogDetectTask()
		So(err, ShouldBeNil)
	})
}

func TestBinlogProcessDetect(t *testing.T) {
	common.Config.MdcServerId = 123
	Convey("binlogProcessDetect：成功.", t, func() {
		binlogProcessDetect()
	})
}
