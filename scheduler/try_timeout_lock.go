package scheduler

import "time"

/*
  @Author: nan<PERSON><PERSON><PERSON><PERSON>@duxiaoman.com
  @Date: 2022/9/13 4:13 下午
  使用chan构建可超时锁.
*/
type TryLock chan struct{}

func NewTryLock() TryLock {
	ch := make(chan struct{}, 1)
	return ch
}

func (l *TryLock) Lock() {
	*l <- struct{}{}
}

func (l *TryLock) UnLock() {
	<-(*l)
}

func (l *TryLock) TryLock() bool {
	select {
	case *l <- struct{}{}:
		return true
	default:
		return false
	}
}

func (l *TryLock) TryLockWithTimeOut(d time.Duration) bool {
	t := time.NewTimer(d)
	select {
	case <-t.C:
		return false
	case *l <- struct{}{}:
		t.Stop()
		return true
	}
}