package scheduler

import (
	"sync"

	"mdc-server/common"
	"mdc-server/data"
)

func init() {
	msMonitorTimerController = new(msMonitorTimer)
	bpMonitorTimerController = new(bpMonitorTimer)
	ctMonitorTimerController = new(ctMonitorTimer)
	retryUpLoadTimerController = new(retryUploadTimer)
}

func Start(mainWg *sync.WaitGroup) {
	role, err := data.CheckRole(common.Config.MdcServerId)
	if err != nil {
		common.Log.Error("check ms role failed, err=[%v]", err)
		return
	}
	//对ms身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		//mdc-server从实例只开启mdc-server探测
		mainWg.Done()
		mainWg.Done()
		go msMonitorTimerController.Start(mainWg)
		common.Log.Warn("Ms server role=[%v]", role)
	case uint8(common.MS_MASTER):
		// serverHA 探测(10s)
		go msMonitorTimerController.Start(mainWg)
		// binlogServer进程探测(600S)
		go bpMonitorTimerController.Start(mainWg)
		// 清理过期备份数据进程(1DAY)
		go ctMonitorTimerController.Start(mainWg)
		// 重试siod上传(1 HOUR)
		go retryUpLoadTimerController.Start(mainWg)
	default:
		common.Log.Warn("unknown ms role=[%v]", role)
	}
}

//关闭定时任务，所有定时任务都关闭成功才会返回true
func Stop() {
	msMonitorTimerController.Stop()
	bpMonitorTimerController.Stop()
	ctMonitorTimerController.Stop()
	retryUpLoadTimerController.Stop()
}
