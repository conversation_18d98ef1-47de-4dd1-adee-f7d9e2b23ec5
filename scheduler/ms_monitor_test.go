package scheduler

import (
	"testing"

	dao_mdc "dt-common/dao/mdc"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func TestGetLocalIp(t *testing.T) {
	Convey("GetLocalIp：成功.", t, func() {
		ip, err := GetLocalIp()
		So(err, ShouldBeNil)
		So(ip, ShouldNotBeNil)
	})
}

func TestMsRegister(t *testing.T) {
	<PERSON>vey("MsRegister：成功.", t, func() {
		err := MsRegister(123)
		So(err, ShouldBeNil)
	})
}

func TestMsHealthCheck(t *testing.T) {
	Convey("msHealthCheck：成功.", t, func() {
		err := msHealthCheck(123)
		So(err, ShouldBeNil)
	})
}

func TestRegisterHeartbeatToDb(t *testing.T) {
	msMonitor := dao_mdc.MsMonitor{
		MdcServerId: 9999,
		Role:        uint8(common.MS_MASTER),
		Status:      uint8(0),
	}
	msMonitor.InsertOneRecord()
	<PERSON><PERSON>("Test RegisterHeartbeatToDb successful...", t, func() {
		err := registerHeartbeatToDb(msMonitor.MdcServerId)
		So(err, ShouldBeNil)
	})
	msMonitor.DeleteByCondCols([]string{"TinkerId"})
	Convey("Test RegisterHeartbeatToDb failed...", t, func() {
		err := registerHeartbeatToDb(msMonitor.MdcServerId)
		So(err, ShouldBeError)
	})
}

func TestCsHealthCheck(t *testing.T) {
	//global.NoReentryTask = append(global.NoReentryTask, protobuf.TaskType_CUSTOMIZE_SHELL)
	common.Config.MdcServerMonitorHeartbeatTimeout = 5
	common.Config.MdcServerId = 2019
	msMonitor := dao_mdc.MsMonitor{
		MdcServerId: 2019,
		Role:        uint8(common.MS_MASTER),
		Status:      uint8(0),
	}
	msMonitor.DeleteByCondCols([]string{"MdcServerId"})
	msMonitor.InsertOneRecord()
	Convey("Test can't find MsMonitorTimer ...", t, func() {
		err := msHealthCheck(990999)
		So(err, ShouldBeError)
	})
	Convey("Test find MsMonitorTimer role is master...", t, func() {
		err := msHealthCheck(msMonitor.MdcServerId)
		So(err, ShouldBeNil)
	})

	msMonitor.Role = uint8(common.MS_SLAVE)
	msMonitor.UpdateByIndexs([]string{"Role"})
	Convey("Test find CsMonitorTimer role is slave ...", t, func() {
		err := msHealthCheck(msMonitor.MdcServerId)
		So(err, ShouldBeNil)
	})
	msMonitor.Role = uint8(10)
	msMonitor.UpdateByIndexs([]string{"Role"})
	Convey("Test find CsMonitorTimer role abnormal ...", t, func() {
		err := msHealthCheck(msMonitor.MdcServerId)
		So(err, ShouldBeError)
	})

}
