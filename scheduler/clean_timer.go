package scheduler

import (
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

//清理任务 探测结构体
type ctMonitorTimer struct {
	Tick     *time.Ticker
	StopChan chan bool
}

var ctMonitorTimerController *ctMonitorTimer

//启动清理探测协程
func (h *ctMonitorTimer) Start(wg *sync.WaitGroup) {
	h.Tick = time.NewTicker(time.Second * time.Duration(common.Config.CleanDataStep))
	h.Stop<PERSON>han = make(chan bool)
	common.Log.Notice("Start scaning ctMonitorTimer... ")
	for {
		select {
		case <-h.Tick.C:
			cleanBkAndRsData()
		case stop := <-h.StopChan:
			if stop {
				common.Log.Notice("Receive exit signal. Stop scaning ctMonitorTimer.")
				wg.Done()
				return
			}
		}
	}
}

//关闭清理探测协程
func (h *ctMonitorTimer) Stop() {
	h.Tick.Stop()
	h.StopChan <- true
	close(h.StopChan)
}

//定时清理备份或恢复数据函数入口
func cleanBkAndRsData() {
	//ms角色认证，由master ms进行定时清理操作
	role, err := data.CheckRole(common.Config.MdcServerId)
	if err != nil {
		common.Log.Warn("check cs role failed, err=[%v]", err)
		return
	}
	//对cs身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		return
	case uint8(common.MS_MASTER):
		//创建清理数据任务并放到任务队列中
		createCleanDataTask()
	default:
		common.Log.Warn("unknown cs role=[%v]", role)
	}
	return
}

//创建清理任务
func createCleanDataTask() {
	if err := createCleanBkData(); err != nil {
		common.Log.Info("createCleanBkData failed,err =[%v]", err)
	}
	//if err := createCleanRsData(); err != nil {
	//	common.Log.Info("createCleanRsData failed,err =[%v]", err)
	//}
	// 清理过期binlog(已打包上传bos的)
	if err := createCleanBinlogFile(); err != nil {
		common.Log.Info("createCleanBinlog failed,err =[%v]", err)
	}
}

//创建备份清理任务
func createCleanBkData() error {
	//查询获得xtrabackup备份上传SIOD_BOS成功满足清理要求的大磁盘备份
	err, xtraTaskInfo := data.GetCleanXtraDataTaskList()
	if err != nil {
		common.Log.Info("CleanXtraDataTaskList is empty")
		return err
	}
	if len(xtraTaskInfo) > 0 {
		for _, cleanTask := range xtraTaskInfo {
			// 目录级别删除
			cleanPath := formatCleanDir(cleanTask.BkRemotePath)
			if cleanPath == "" {
				errMsg := fmt.Sprintf("format clean dir, clusterId=[%v],clusterName=[%v],cleanPath is nil", cleanTask.ClusterId, cleanTask.ClusterName)
				common.Log.Error(errMsg)
				return errors.New(errMsg)
			}
			//构建请求发送到消息队列
			msg := &pb_server.XwebPlusFrontAsyncMsg{
				TaskType: pb_server.MdcServerTaskType_CLEAN_BRDATA,
				BaseMessage: &pb_server.MdcBaseMessage{
					ClusterId:   cleanTask.ClusterId,
					ClusterName: cleanTask.ClusterName,
					NodeId:      cleanTask.NodeId,
				},
				MsgType: &pb_server.XwebPlusFrontAsyncMsg_CleanDataTaskReqMsg{
					CleanDataTaskReqMsg: &pb_server.CleanDataTaskReqMsg{
						TaskId:        cleanTask.TaskId,
						InstanceIp:    cleanTask.BkRemoteIp,
						CleanDataType: pb_server.DataType_BK_REMOTE_DATA,
						DataDir:       []string{cleanPath},
					},
				},
			}
			err = communicator.PutMsMsgToChan(*msg)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to put msg to msgchan,clusterId=[%v],clusterName=[%v],err=[%v]", cleanTask.ClusterId, cleanTask.ClusterName, err)
				common.Log.Error("[Mdc-Server] ErrorMsg=[%v] CalledError=[%v]", common.ErrMsMsgToChannel, errMsg)
				return err
			}
		}
	}
	//查询获得xtrabackup备份上传DBA_BOS成功满足清理要求的bos备份
	err, xtraTaskInfo = data.GetCleanBOSDataTaskList()
	if err != nil {
		common.Log.Info("CleanBOSDataTaskList is empty")
		return err
	}
	if len(xtraTaskInfo) > 0 {
		for _, cleanTask := range xtraTaskInfo {
			cleanDir := []string{cleanTask.BkBosPath}
			if cleanTask.BkBinlogBosPath != "" {
				cleanDir = append(cleanDir, cleanTask.BkBinlogBosPath)
			}
			//构建请求发送到消息队列
			msg := &pb_server.XwebPlusFrontAsyncMsg{
				TaskType: pb_server.MdcServerTaskType_CLEAN_BRDATA,
				BaseMessage: &pb_server.MdcBaseMessage{
					ClusterId:   cleanTask.ClusterId,
					ClusterName: cleanTask.ClusterName,
					NodeId:      cleanTask.NodeId,
				},
				MsgType: &pb_server.XwebPlusFrontAsyncMsg_CleanDataTaskReqMsg{
					CleanDataTaskReqMsg: &pb_server.CleanDataTaskReqMsg{
						TaskId:        cleanTask.TaskId,
						InstanceIp:    cleanTask.BkRemoteIp,
						CleanDataType: pb_server.DataType_BOS_DATA,
						DataDir:      cleanDir,
					},
				},
			}
			err = communicator.PutMsMsgToChan(*msg)
			if err != nil {
				errMsg := fmt.Sprintf("Failed to put msg to msgchan,clusterId=[%v],clusterName=[%v],err=[%v]", cleanTask.ClusterId, cleanTask.ClusterName, err)
				common.Log.Error("[Mdc-Server] ErrorMsg=[%v] CalledError=[%v]", common.ErrMsMsgToChannel, errMsg)
				return err
			}
		}
	}
	return nil
}

// format backup dir
func formatCleanDir(remotePath string) string {
	pathList := strings.Split(remotePath, "/")
	var removeDir string
	if len(pathList) != 0 {
		for idx, dirName := range pathList[:len(pathList)-1] {
			if idx == 0 {
				continue
			}
			if dirName == "" {
				common.Log.Error("format backup dir is nil")
				return ""
			}
			if idx == 1 {
				removeDir = fmt.Sprintf("/%v", dirName)
			} else {
				removeDir = fmt.Sprintf(removeDir+"/%v", dirName)
			}
		}
	}
	return removeDir
}

//创建恢复清理任务
func createCleanRsData() error {
	//获得满足清理时间要求的任务列表
	err, xtraTaskInfo := data.GetCleanRsDataTaskList()
	if len(xtraTaskInfo) <= 0 || err != nil {
		common.Log.Info("CleanRsDataTaskList is empty")
		return err
	}
	for _, cleanTask := range xtraTaskInfo {
		taskTime, err := time.Parse(common.TimeFormat, cleanTask.TaskUpdateTime)
		if err != nil {
			common.Log.Info("Parse time failed,err=[%v]", err)
			continue
		}
		//判断是否超过保存时间
		if taskTime.Before(time.Now().AddDate(0, 0, -cleanTask.RestoreSaveDays)) {
			continue
		}
		msg := new(pb_server.XwebPlusFrontAsyncMsg)
		//构建请求发送到消息队列
		if cleanTask.RestoreType == int32(common.ParseBinlog) {
			msg = &pb_server.XwebPlusFrontAsyncMsg{
				TaskType: pb_server.MdcServerTaskType_CLEAN_BRDATA,
				BaseMessage: &pb_server.MdcBaseMessage{
					ClusterId:   cleanTask.ClusterId,
					ClusterName: cleanTask.ClusterName,
					NodeId:      cleanTask.NodeId,
				},
				MsgType: &pb_server.XwebPlusFrontAsyncMsg_CleanDataTaskReqMsg{
					CleanDataTaskReqMsg: &pb_server.CleanDataTaskReqMsg{
						TaskId:        cleanTask.Id,
						InstanceIp:    cleanTask.RsRemoteIp,
						CleanDataType: pb_server.DataType_BOS_DATA,
						DataDir:       []string{cleanTask.BinlogSqlPath},
					},
				},
			}
		} else {
			msg = &pb_server.XwebPlusFrontAsyncMsg{
				TaskType: pb_server.MdcServerTaskType_CLEAN_BRDATA,
				BaseMessage: &pb_server.MdcBaseMessage{
					ClusterId:   cleanTask.ClusterId,
					ClusterName: cleanTask.ClusterName,
					NodeId:      cleanTask.NodeId,
				},
				MsgType: &pb_server.XwebPlusFrontAsyncMsg_CleanDataTaskReqMsg{
					CleanDataTaskReqMsg: &pb_server.CleanDataTaskReqMsg{
						TaskId:        cleanTask.Id,
						InstanceIp:    cleanTask.RsRemoteIp,
						CleanDataType: pb_server.DataType_RS_REMOTE_DATA,
						DataDir:       []string{cleanTask.RestorePath},
					},
				},
			}
		}
		err = communicator.PutMsMsgToChan(msg)
		if err != nil {
			errMsg := fmt.Sprintf("Failed to put msg to msgchan,clusterId=[%v],clusterName=[%v],err=[%v]", cleanTask.ClusterId, cleanTask.ClusterName, err)
			common.Log.Error("[Mdc-Server] ErrorMsg=[%v] CalledError=[%v]", common.ErrMsMsgToChannel, errMsg)
			continue
		}
	}
	return nil
}

// 创建清理binlog任务
func createCleanBinlogFile() error {
	// 获取已打包上传binlog信息
	overdueBinlogInfo, err := data.GetUploadBinlogRecord()
	if err != nil {
		errMsg := fmt.Sprintf("failed to get binlog for packing backup, err=[%v]", err)
		common.Log.Error(errMsg)
		return errors.New(errMsg)
	}
	// 过期记录为空
	if len(overdueBinlogInfo) == 0 {
		common.Log.Info("CleanUploadBinlog is empty")
		return err
	}

	// 进行过期记录删除操作
	for _, sigInfo := range overdueBinlogInfo {
		//删除备库实例上的备份的binlog
		bkReq := pb_agent.MdcAgentSyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_DELETE_BINLOGBK,
			MsgType: &pb_agent.MdcAgentSyncMsg_DeleteBinlogBk{
				DeleteBinlogBk: &pb_agent.DeleteBinlogBk{
					BkBinlogDir:          sigInfo.RemotePath,
					BinlogFileNameResult: sigInfo.BinlogFileName,
				},
			},
		}
		// 获取发起rpc任务的rpc地址及执行任务的agentIP
		// 已上传bos成功的binlog在大磁盘机器上进行删除, 默认请求都是发送至大磁盘 使用默认端口
		agentAddr := fmt.Sprintf("%s:%d", sigInfo.RemoteIp, common.Config.AgentPort)
		_, err := communicator.SendSyncMsgToDagent(bkReq, agentAddr)
		if err != nil {
			errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
			common.Log.Warn(errMsg)
			// 删除失败跳过即可
			continue
		}
	}
	return nil
}
