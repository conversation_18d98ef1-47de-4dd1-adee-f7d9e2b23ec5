package scheduler

import (
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	pb "dt-common/protobuf/mdc-server"
	"github.com/robfig/cron/v3"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

type ClusterXtraTask struct {
	//集群每个分片对应的热备任务map key为clusterId_nodeId value为任务id
	ClustrXtraTaskMap map[string]cron.EntryID
	Mu                sync.RWMutex
	Cron              *cron.Cron
}

//备份任务管理结构
var ClusterXtraManage = &ClusterXtraTask{
	ClustrXtraTaskMap: make(map[string]cron.EntryID, 0),
	Cron:              cron.New(cron.WithSeconds()),
}

const HmsForMat = "15:04:05"

//初始化定时xtra任务
func StartInitCron(mainWg *sync.WaitGroup) {
	defer mainWg.Done()

	role, err := data.CheckRole(common.Config.MdcServerId)
	if err != nil {
		common.Log.Warn("check ms role failed, err=[%v]", err)
		return
	}
	//对ms身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		return
	case uint8(common.MS_MASTER):
		err, bkTaskInfo := data.GetOnlineTaskList()
		if len(bkTaskInfo) <= 0 || err != nil {
			common.Log.Info("BkOnlineTask is empty")
		}
		for _, onlineTask := range bkTaskInfo {
			if err = AddCronTask(onlineTask.BkStartTime, onlineTask.ClusterId, onlineTask.NodeId); err != nil {
				common.Log.Warn("AddCronTask failed,err=[%v],clusterName=[%v],clusterId=[%v],nodeId=[%v]",
					err, onlineTask.ClusterName, onlineTask.ClusterId, onlineTask.NodeId)
				continue
			}
		}
	default:
		common.Log.Warn("unknown ms role=[%v]", role)
	}

	//启动定时任务
	ClusterXtraManage.Cron.Start()
	return
}

//添加某个集群的定时任务
func AddCronTask(startTime string, clusterId int64, nodeId int64) error {
	if startTime == "" || clusterId < 0 || nodeId < 0 {
		errMsg := fmt.Sprintf("task parameter is illegal,startTime=[%v],clusterId=[%v],nodeId=[%v]", startTime, clusterId, nodeId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//根据时间生成对应的spec
	spec := GeneSpec(startTime)
	if spec == "" {
		errMsg := fmt.Sprintf("Add Cron func failed, spec=[%v],clusterId=[%v]", spec, clusterId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//添加备份任务
	entryID, err := ClusterXtraManage.Cron.AddFunc(spec, func() {
		StartBackupTask(clusterId, nodeId)
	})
	if err != nil {
		common.Log.Warn("Add Cron func failed, err=[%v],clusterId=[%v]", err, clusterId)
		return err
	}
	if ClusterXtraManage.ClustrXtraTaskMap == nil {
		ClusterXtraManage.ClustrXtraTaskMap = make(map[string]cron.EntryID)
	}
	keyStr := fmt.Sprintf("%v_%v", clusterId, nodeId)
	if val, ok := ClusterXtraManage.ClustrXtraTaskMap[keyStr]; ok {
		//已有该集群对应的备份任务 需要先删除旧的定时任务
		ClusterXtraManage.Cron.Remove(val)
	}
	ClusterXtraManage.Mu.Lock()
	ClusterXtraManage.ClustrXtraTaskMap[keyStr] = entryID
	ClusterXtraManage.Mu.Unlock()
	return nil
}

//删除某个集群的定时任务
func DelCronTask(clusterId int64, nodeId int64) error {
	if clusterId < 0 || nodeId < 0 {
		errMsg := fmt.Sprintf("task parameter is illegal,clusterId=[%v],nodeId=[%v]", clusterId, nodeId)
		return errors.New(errMsg)
	}
	keyStr := fmt.Sprintf("%v_%v", clusterId, nodeId)
	if val, ok := ClusterXtraManage.ClustrXtraTaskMap[keyStr]; ok {
		ClusterXtraManage.Cron.Remove(val)
		ClusterXtraManage.Mu.Lock()
		delete(ClusterXtraManage.ClustrXtraTaskMap, keyStr)
		ClusterXtraManage.Mu.Unlock()
		errMsg := fmt.Sprintf("Delete cron task success,ClusterStr=[%v]", keyStr)
		common.Log.Warn(errMsg)
	} else {
		errMsg := fmt.Sprintf("Delete cron task from map failed,ClusterStr=[%v] is not exist", keyStr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//解析时间 生成对应的cron spec
func GeneSpec(startTime string) string {
	if startTime == "" {
		return ""
	}

	ti, err := time.Parse(HmsForMat, startTime)
	if err != nil {
		errMsg := fmt.Sprintf("parse time failed,err=[%v],startTime=[%v]", err, startTime)
		common.Log.Warn(errMsg)
		return ""
	}
	h := ti.Hour()
	m := ti.Minute()
	s := ti.Second()
	specByte := []byte{}
	specByte = append(specByte, []byte(strconv.Itoa(s))...)
	specByte = append(specByte, ' ')
	specByte = append(specByte, []byte(strconv.Itoa(m))...)
	specByte = append(specByte, ' ')
	specByte = append(specByte, []byte(strconv.Itoa(h))...)
	specByte = append(specByte, ' ')
	specByte = append(specByte, '*', ' ', '*', ' ', '*')
	return string(specByte)
}

//开启集群对应的备份任务
func StartBackupTask(clusterId int64, nodeId int64) {
	//查询备份任务
	err, bkTaskInfo := data.GetBkTaskInfo(clusterId, nodeId)
	if err != nil || bkTaskInfo == nil {
		errMsg := fmt.Sprintf("Get backup task info failed, error=[%v],ClusterId=[%v], ClusterName=[%v],NodeId=[%v]", err, bkTaskInfo.ClusterId, bkTaskInfo.ClusterName, bkTaskInfo.NodeId)
		common.Log.Warn(errMsg)
		return
	}
	//构建请求发送到消息队列
	startBkMsg := &pb.XwebPlusFrontAsyncMsg{
		TaskType: pb.MdcServerTaskType_START_BACKUP,
		BaseMessage: &pb.MdcBaseMessage{
			ClusterId:   bkTaskInfo.ClusterId,
			ClusterName: bkTaskInfo.ClusterName,
			NodeId:      bkTaskInfo.NodeId,
		},
		MsgType: &pb.XwebPlusFrontAsyncMsg_XwebStartBackupTaskReqMsg{
			XwebStartBackupTaskReqMsg: &pb.XwebStartBackUpTaskReqMsg{
				BkMysqlBasedir: bkTaskInfo.BkMysqlBasedir,
				BkType:         bkTaskInfo.BkType,
				FullDay:        bkTaskInfo.FullDay,
				BkOpt:          bkTaskInfo.BkOpt,
			},
		},
	}
	err = communicator.PutMsMsgToChan(*startBkMsg)
	if err != nil {
		common.Log.Error("[Mdc-Server] ErrorMsg=[%v] CalledError=[%v]", common.ErrMsMsgToChannel, err)
	}
}
