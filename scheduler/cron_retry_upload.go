package scheduler

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	dao_mdc "dt-common/dao/mdc"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-server/common"
	"mdc-server/data"
)

//此定时器每隔一小时扫描未成功上传到灾备平台的备份集合进行上传重试
// 队列已满
var (
	SIOD_QUEUE_FULL = errors.New("SIOD QUEUE FULL")
	tryLock         = NewTryLock()
)

// 重试上传灾备
type retryUploadTimer struct {
	Ticker   *time.Ticker
	StopChan chan bool
}

var retryUpLoadTimerController *retryUploadTimer

// 启动重试上传协程
func (r *retryUploadTimer) Start(wg *sync.WaitGroup) {
	r.Ticker = time.NewTicker(time.Second * time.Duration(common.Config.RetryUploadSIODStep))
	r.<PERSON><PERSON><PERSON> = make(chan bool)
	common.Log.Notice("Start scaning retryUpLoadTimer ...")
	for {
		select {
		case <-r.Ticker.C:
			if tryLock.TryLockWithTimeOut(10 * time.Second) {
				retryUploadSiodBos()
				tryLock.UnLock()
			} else {
				common.Log.Info("TryLockWithTimeOut. can't get retry lock.")
			}
		case stop := <-r.StopChan:
			if stop {
				common.Log.Notice("Receive exit signal. Stop scaning retryUpLoadTimerController.")
				wg.Done()
				return
			}
		}
	}
}

// 关闭重试上传
func (r *retryUploadTimer) Stop() {
	r.Ticker.Stop()
	r.StopChan <- true
	close(r.StopChan)
}

func retryUploadSiodBos() {
	// 获取未成功上传SIOD集合
	err, uploadSiodInfo := data.GetFailUploadSiod()
	if err != nil {
		common.Log.Error("Get uploadSIOD data failed, err=[%v]", err)
		return
	}

	if len(uploadSiodInfo) <= 0 {
		common.Log.Warn("upload siod failed gather is empty")
		return
	}

	for _, uploadSiod := range uploadSiodInfo {
		err, xtraTask := data.GetXtraTaskForCluster(uploadSiod.ClusterId, uploadSiod.NodeId)
		if err != nil {
			common.Log.Error("retry upload siod bos failed for get cluster xtraTask. clusterID=[%v] nodeID=[%v] err=[%v] id=[%v]",
				uploadSiod.ClusterId, uploadSiod.NodeId, err, uploadSiod.Id)
			continue
		}
		err = retryUpload(xtraTask, uploadSiod)
		if err != nil  {
			if  err == SIOD_QUEUE_FULL {
				common.Log.Warning("siod task full. err=[%v] id=[%v]", err, uploadSiod.Id)
				return
			} else {
				common.Log.Error("retry upload siod bos failed. err=[%v] id=[%v]", err, uploadSiod.Id)
				continue
			}
		}
	}
}

func retryUpload(xtraTask *dao_mdc.BkTaskInfo, taskInfo *dao_mdc.BkXtraTaskExecRecord) error {
	var (
		err           error
		marshalByte   []byte
		siodBkRequest common.SiodBkRequest
		resp          common.SiodPlatResult
	)

	dataVersion := common.GetDataVersion(taskInfo.BkBosPath)
	if dataVersion == "" {
		errMsg := fmt.Sprintf("GetXtraTask data version failed, dataVersion=[%v] error=[%v]", dataVersion, err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}

	// 构造请求体
	siodBkRequest = common.SiodBkRequest{
		Platform:    common.Config.SiodBackupPlatform,
		Token:       common.Config.SiodRegisterToken,
		ProtocolID:  xtraTask.ProtocolId,
		ProductName: common.SiodBackupProductName,
		DataVersion: dataVersion,
		AppName:     xtraTask.BkBns,
	}

	if marshalByte, err = json.Marshal(siodBkRequest); err != nil {
		errMsg := fmt.Sprintf("marshal siodBkRequest failed, error=[%v]", err)
		common.Log.Error(errMsg)
		return errors.New(errMsg)
	}

	resp, err = common.HTTPPost(common.Config.SiodBackupManualRun, bytes.NewReader(marshalByte))
	if err != nil {
		errMsg := fmt.Sprintf("post backup request failed, error=[%v], resp=[%+v], cluster_name=[%v],siodBkRequest=[%v]", err, resp,
			xtraTask.ClusterName, siodBkRequest)
		common.Log.Error(errMsg)
		logStr := fmt.Sprintf("mdc-server start siod backup requset failed,%v", errMsg)
		if err := data.UpdateXtraTaskStatus(taskInfo.TaskId, pb_server.XtraTaskStatus_UploadSiodBosFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", xtraTask.ClusterName, taskInfo.TaskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg)
		}
		return errors.New(errMsg)
	}

	if resp.Errno != 0 {
		switch resp.Errno {
		// todo 重复请求 返回对应任务ID进行回填
		case 2014:
			errMsg := fmt.Sprintf("post backup request failed, error=[%v], resp=[%+v], cluster_name=[%v],siodBkRequest=[%v]", err, resp,
				xtraTask.ClusterName, siodBkRequest)
			common.Log.Error(errMsg)
			logStr := fmt.Sprintf("mdc-server has been completed post siod requse, %v", errMsg)
			// 将任务状态置为已接受到请求
			if err := data.UpdateXtraTaskStatus(taskInfo.TaskId, pb_server.XtraTaskStatus_UploadSiodBosReicer, logStr); err != nil {
				errMsg = fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", xtraTask.ClusterName, taskInfo.TaskId, err)
				common.Log.Warning(errMsg)
				return errors.New(errMsg)
			}
			return nil
		// 其余请求异常场景(2001请求体异常 2002请求队列已满 等等)
		default:
			errMsg := fmt.Sprintf("post backup request failed, error=[%v], resp=[%+v], cluster_name=[%v],siodBkRequest=[%v]", err, resp,
				xtraTask.ClusterName, siodBkRequest)
			common.Log.Error(errMsg)
			logStr := fmt.Sprintf("mdc-server start siod backup requset failed,%v", errMsg)
			if err := data.UpdateXtraTaskStatus(taskInfo.TaskId, pb_server.XtraTaskStatus_UploadSiodBosFail, logStr); err != nil {
				errMsg = fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", xtraTask.ClusterName, taskInfo.TaskId, err)
				common.Log.Warning(errMsg)
				return errors.New(errMsg)
			}
			if resp.Errno == 2002 {
				return SIOD_QUEUE_FULL
			}
			return errors.New(errMsg)
		}
	}

	// 成功修改任务状态
	logStr := fmt.Sprintf("mdc-server start siod backup requset successful")
	if err = data.UpdateXtraTaskStatus(taskInfo.TaskId, pb_server.XtraTaskStatus_UploadSiodBosReicer, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", xtraTask.ClusterName, taskInfo.TaskId, err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}

	err, siodTaskId := common.GetRespDataVal(resp.Data)
	if err != nil {
		errMsg := fmt.Sprintf("getRespDataVal fail, cluster_name=[%v], taskId=[%+v], err=[%v]", xtraTask.ClusterName, taskInfo.TaskId, err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}
	// 更新siodTaskID
	xtraRecordTask := new(dao_mdc.BkXtraTaskExecRecord)
	xtraRecordTask.TaskId = taskInfo.TaskId
	xtraRecordTask.ClusterId = taskInfo.ClusterId
	xtraRecordTask.NodeId = taskInfo.NodeId
	xtraRecordTask.SiodTaskInfoId = siodTaskId
	//需要更新的列
	conds := []string{"TaskId", "ClusterId", "NodeId"}
	cols := []string{"SiodTaskInfoId"}
	_, err = xtraRecordTask.UpdateByCondCols(conds, cols)
	if err != nil {
		common.Log.Warn("failed to update xtraTask info. clusterId=[%v] nodeId=[%v] err=[%v]", xtraTask.ClusterId, xtraTask.NodeId, err)
		return err
	}
	common.Log.Info("upload to siod success. resp=[%+v] siodBkRequest=[%+v]", resp, siodBkRequest)
	return nil
}
