# mdc需求澄清

mysql备份/恢复服务

## 0 项目架构

+--------------------+      +--------------------+
|     mdc-server     | ---> |     mdc-agent      |
+--------------------+      +--------------------+
                                      |
                                      |
                            +--------------------+
                            |   mdc-agent(file)  |
                            +--------------------+

- mdc-server: 部署在任意机器上，负责管理、下发备份/恢复任务的server
- mdc-agent: 部署在mysql实例上，负责备份/恢复任务的agent
- mdc-agent(file): 部署在大磁盘上，负责备份文件管理的agent

## 1 功能设计

### 1.1 数据备份

- 数据备份以分片为单位，不同类型的集群分片数不同：
  - 单实例集群-> 1分片
  - DDBS -> N分片
  - FDB -> 1分片

- 备份内容分为：
  - 全量数据备份：使用xtrabackup开源工具
  - 增量数据备份：同上，但依赖上一次的备份文件才能做增量备份
  - binlog增量备份：超出限制后切出来的binlog文件

- 备份默认在「备机」上进行，但用户若是指定了备份实例，则在指定实例上进行。更改备份实例时，会立刻触发一次全量备份。

- 备份任务由对应机器上的mdc-agent实际执行，mdc-server发起异步调用，mdc-agent负责维护任务的状态，并在成功or失败时汇报给mdc-server

- **备份任务的详细流程（mdc-agent）：**
  - Pre Check
    - 如果是全量数据备份，清理目录下的旧文件
    - 如果是增量数据备份，检查上一次的备份文件在不在，如果不在，需要去大磁盘机器上下载
  - Exec
    - 如果是binlog备份，将binlog文件按时间倒排，依次检查并找到没有备份过的文件
    - 如果是数据备份，执行xtrabackup备份
  - Exec 单独备份user表
  - Compress 压缩备份文件
  - Upload 上传
    - 同步上传到大磁盘，7天存储
    - 异步上传到华北BOS，这一步是由大磁盘上的agent负责完成的，30天存储
    - 异步上传到华南BOS，这一步是由大磁盘上的agent负责完成的，180天存储
  - Return 返回success结果

### 1.2 数据恢复

- 恢复任务由对应机器上的mdc-agent实际执行，下发为异步任务，mdc-agent负责维护任务的状态，并在成功or失败时汇报给mdc-server

- 数据恢复时根据恢复到的时间点，自动选择恢复数据的来源：
  - 天级恢复：利用xtrabackup将备份的库表数据恢复到指定mysql实例
  - 秒级恢复：在天级恢复的基础上，解析binlog文件，逐条执行sql语句直到指定的时间点

- **恢复任务的详细流程（mdc-agent）：**
  - Pre Check：检查是否有足够的备份文件
    - 如果是天级恢复：检查最近一次的全量数据文件、以及往后每天的增量数据文件是否可用
    - 如果是秒级恢复：检查从最后一个数据备份文件开始，到指定恢复时间点的所有binlog文件是否连续，是否可用
  - Download
    - 下载上述所需的所有备份
  - Uncompress 解压
  - Exec
    - 利用xtrabackup开源工具恢复数据
    - 覆盖user表
    - 利用mysqlbinlog工具回放binlog
  - Return 返回success结果
  
### 1.3 定时任务

- 定时任务分为：
  - 全量数据备份任务：通常为每周执行一次
  - 增量数据备份任务：通常为每天执行一次
  - binlog备份任务：通常为每小时执行一次

- 出于稳定性考量，mdc-server会部署至少2个实例，为避免定时任务的重复执行
  - 负载均衡策略：每个mdc-server上跑不同集群的定时任务，每10s一刷新
  - 互斥锁策略：同时只会有一个mdc-server在跑定时任务

### 1.4 文件管理（mdc-agent）

- 文件上传
  - 通过sftp协议接收文件上传，提供空间演算接口，判断是否有足够空间存下文件
  - 演算时，不仅要考虑当前的磁盘利用率，还需要把已经在传输的文件大小计算在内
  - 大磁盘机器会有多块盘，每块盘的利用率不一样，每次接收文件存储时选择利用率最低的盘

- 文件下载
  - 允许下载本地文件

- BOS上传
  - 在完整接收到文件后，将文件上传到华北BOS、华南BOS

- 定时删除
  - 本地文件，有效期7天
  - 华北BOS，有效期30天
  - 华南BOS，有效期180天

## 2 数据设计

### 2.1 bk_task 备份任务表

Name               | Type   | Comment
---                | ---    | ---
cluster_id         | int64  | 集群id
type               | string | 备份类型：data_full/data_incr/binlog
cron               | string | 定时频率
status             | int32  | 备份状态：normal、stopped

### 2.2 bk_file 备份文件表

Name                      | Type   | Comment
---                       | ---    | ---
cluster_id                | int64  | 集群id
node_id                   | int64  | 分片id
ip                        | string | 备份实例ip
type                      | string | 备份类型：data_full/data_incr/binlog
file_name                 | string | 文件名
file_size                 | int32  | 文件大小，单位B
file_last_modify_time     | time   | 文件最后修改时间
remote_ip                 | string | 大磁盘机器ip
remote_path               | string | 大磁盘保存路径
remote_expired_at         | time   | 大磁盘过期时间
short_term_bos_url        | string | 华北BOS下载地址
short_term_bos_expired_at | time   | 华北BOS过期时间
long_term_bos_url         | string | 华南BOS下载地址
long_term_bos_expired_at  | time   | 华南BOS过期时间
status                    | int32  | 备份状态：init、normal、error、expried
created_at                | time   | 记录创建时间
updated_at                | time   | 记录最近一次更新时间

### 2.3 bk_recovery 恢复记录表

Name               | Type   | Comment
---                | ---    | ---
cluster_id         | int64  | 集群id
node_id            | int64  | 分片id
ip                 | string | 恢复实例ip
scope              | string | 恢复粒度：day天级/second秒级
files              | json   | 使用到的文件[string]
status             | int32  | 备份状态：init、normal、error

## 3 接口设计

### 3.1 mdc-server

#### 3.1.1 创建定时任务

#### 3.1.2 更换备份实例

#### 3.1.3 数据恢复

#### 3.1.3 获取备份文件列表

### 3.2 mdc-agent

#### 3.2.1 备份

#### 3.2.2 恢复

### 3.3 mdc-agent(file)

#### 3.3.1 空间计算

#### 3.3.2 上传

#### 3.3.3 获取磁盘利用率


## 4 命令

./xtrabackup --defaults-file=/path/to/my.cnf --socket=/path/to/mysql.sock -uroot -p'your_password' --compress --compress-threads=4 --parallel=16 --backup --slave-info --lock-ddl-per-table --stream=xbstream | ssh user@remote_host "cat > /path/to/backup.xbstream" > xtrabackup_output.log 2> xtrabackup_error.log

--incremental --incremental-basedir=/path/to/full_backup

## 附：阿里云能力

[SQL备份与回滚（公测中）](https://help.aliyun.com/zh/dms/sql-backup-and-rollback)

对比项 | 数据追踪 | SQL备份与回滚
---|---|---
功能使用 | 需要提交数据追踪工单，配置时间范围、表名、操作类型等工单参数。| 无需进行额外的工单配置。仅需在执行SQL时，开启备份，待获取备份数据后，直接在SQL Console执行即可。
回滚精确度 | 需要在一段时间范围内筛选出符合表的变更条件的SQL，再从中寻找目标。 | 针对MySQL数据库可精确定位到误操作的SQL，直接生成回滚脚本。非MySQL的关系型数据库需要手动回滚数据。
实例的能力要求 | 不限制实例的能力。 | 实例需要具有自由操作（有5次试用次数）和稳定变更能力（不限制使用次数）。


