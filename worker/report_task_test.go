package worker

import (
	"fmt"
	"io"
	"mdc-server/common"
	"testing"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/communicator"
)

func TestHandleExecXtraReport(t *testing.T) {
	Convey("测试handleExecXtraReport", t, func() {
		patches := ApplyFunc(communicator.SendAsyncMsgToDagent, func(data pb_agent.MdcAgentAsyncMsg, clientIp string) (*pb_agent.MdcAgentAsyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		msg := &pb_server.MdcAgentReportMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_START_XTRA,
			Taskid:           0,
			ErrorMsg:         "",
			ClientIp:         "127.0.0.1",
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		err := handleExecXtraReport(msg)
		So(err, ShouldBeNil)
	})
}

func TestHandleUnpackData(t *testing.T) {
	Convey("测试handleUnpackData", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()

		err := handleUnpackData(1, 193827, 1, "test")
		So(err, ShouldBeNil)
	})
}

func TestRegisterSiodBos(t *testing.T) {
	Convey("测试registerSiodBos", t, func() {
		patches := ApplyFunc(HTTPPost, func(url string, bodys io.Reader) (resp SiodPlatResult, err error) {
			resp.Data = 12345
			resp.Errno = 1
			return
		})
		defer patches.Reset()

		_, err := registerSiodBos("test", 1, 193827, 1)
		So(err, ShouldBeNil)
	})
}

func TestUploadSiodBos(t *testing.T) {
	Convey("测试uploadSiodBos", t, func() {
		patches := ApplyFunc(HTTPPost, func(url string, bodys io.Reader) (resp SiodPlatResult, err error) {
			return
		})
		defer patches.Reset()

		err := uploadSiodBos(12345, "test", 193827, 666462720, 1)
		So(err, ShouldBeNil)
	})
}

func TestHandleDownloadDataReport(t *testing.T) {
	Convey("测试handleDownloadDataReport", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		msg := &pb_server.MdcAgentReportMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA,
			Taskid:           0,
			ErrorMsg:         "",
			ClientIp:         "127.0.0.1",
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		err := handleDownloadDataReport(msg)
		So(err, ShouldNotBeNil)
	})
}

func TestHandleExecRestoreReport(t *testing.T) {
	Convey("测试handleExecRestoreReport", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		msg := &pb_server.MdcAgentReportMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA,
			Taskid:           0,
			ErrorMsg:         "",
			ClientIp:         "127.0.0.1",
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		err := handleExecRestoreReport(msg)
		So(err, ShouldNotBeNil)
	})
}

func TestSendAccountInfoByEmailToUser(t *testing.T) {
	Convey("sendAccountInfoByEmailToUser", t, func() {
		common.InitEmail()
		common.InitUuap()
		taskType := pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG
		accountInfo := AccountEmailInfo{
			Address: fmt.Sprintf("http://sc.duxiaoman-int.com/dbs/dbsIndex?rsTaskId=%v", 1),
		}
		sendAccountInfoByEmailToUser(taskType, accountInfo, []string{"genggangfeng_dxm"})
	})
}
