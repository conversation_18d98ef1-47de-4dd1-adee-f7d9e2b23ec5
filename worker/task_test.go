package worker

import (
	"sync"
	"testing"
	"time"

	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
)

func TestStartWorker(t *testing.T) {
	//主协程等待变量
	waitgroup := new(sync.WaitGroup)
	//启动监听
	waitgroup.Add(1)
	Convey("StartWorker : 启动woker成功", t, func() {
		go StartWorker(1, 1, waitgroup)
	})

	agentMsgFail := &pb_server.XwebPlusFrontAsyncMsg{}
	common.MsServerMsgChan <- &common.MdcServerTaskInfo{agentMsgFail}
	waitgroup.Add(1)
	Convey("StartWorker : 启动woker成功", t, func() {
		go StartWorker(1, 1, waitgroup)
	})

	agentMsg := &pb_server.MdcAgentReportMsg{
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   3308,
			NodeId:      1,
			ClusterName: "test",
		},
		MsgType: &pb_server.MdcAgentReportMsg_ExecXtrabkReport{
			ExecXtrabkReport: &pb_server.ExecXtrabkReport{
				BkLsnFrom: "0",
				BkLsnTo:   "12345",
			},
		},
	}
	common.MsServerMsgChan <- &common.MdcServerTaskInfo{agentMsg}
	waitgroup.Add(1)
	Convey("StartWorker : 启动woker成功", t, func() {
		go StartWorker(1, 1, waitgroup)
	})
	time.Sleep(time.Second * 2)
	Convey("StopWorker : 关闭woker", t, func() {
		StopWorker()
	})
}

func TestHandleMsgChanTask(t *testing.T){
	var msg pb_server.XwebPlusFrontAsyncMsg
	msg.BaseMessage=&pb_server.MdcBaseMessage{
		ClusterName: "",
		ClusterId:   0,
		NodeId:      0,
	}
	msg.TaskType=pb_server.MdcServerTaskType_FRONT_START_BACKUP
	//msg = pb_server.XwebPlusFrontAsyncMsg{
	//	BaseMessage: &pb_server.MdcBaseMessage{
	//		ClusterName: "",
	//		ClusterId:   0,
	//		NodeId:      0,
	//	},
	//	TaskType: pb_server.MdcServerTaskType_FRONT_START_BACKUP,
	//}
	tasks:=common.MdcServerTaskInfo{&msg}
	handleMsgChanTask(&tasks)
}
