package worker

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"sync/atomic"
	"time"

	dt "dt-common/dao"
	dao_mdc "dt-common/dao/mdc"
	"dt-common/global"
	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	pb_tinker "dt-common/protobuf/tinker"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
	"mdc-server/scheduler"
)

var CheckTimeOut int = 12
var (
	XdTaskCounter int64 = 0
	ZfTaskCounter int64 = 0
	ZsTaskCounter int64 = 0
	KjTaskCounter int64 = 0
)

// 计数+1 取模返回可用大磁盘下标
func getMachineIndex(env global.DbsEnv, machineCount int64) int {
	switch env {
	case global.XD_ENV:
		atomic.AddInt64(&XdTaskCounter, 1)
		return int(XdTaskCounter % machineCount)
	case global.ZF_ENV:
		atomic.AddInt64(&ZfTaskCounter, 1)
		return int(ZfTaskCounter % machineCount)
	case global.ZS_ENV:
		atomic.AddInt64(&ZsTaskCounter, 1)
		return int(ZsTaskCounter % machineCount)
	case global.KJ_ENV:
		atomic.AddInt64(&KjTaskCounter, 1)
		return int(KjTaskCounter % machineCount)
	}
	return 0
}

//处理前端发来的开启备份任务请求
func dealXwebStartBackup(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	// 开启事务
	o, beginTrxErr := data.BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Failed to start transaction before update task status. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), beginTrxErr)
		return beginTrxErr
	}
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		data.RollbackTrx(o)
		return err
	}
	//修改备份任务信息表中任务状态
	if infoSlice[0].BkExecStatus != int32(common.BS_ONLINE) {
		infoSlice[0].BkExecStatus = int32(common.BS_ONLINE)
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BkExecStatus"}
		effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
		if nil != err || effectRows != 1 {
			errStr := fmt.Sprintf("Fail to dealXwebStartBackup due to update bkTaskInfo failed. clusterId=[%v]"+
				" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
			common.Log.Error(errStr)
			data.RollbackTrx(o)
			return errors.New(errStr)
		}
	}
	// 如果状态修改成功，提交事务
	comitTrxErr := data.CommitTrx(o)
	if comitTrxErr != nil {
		common.Log.Warn("Update bkTask_status is finish ! Transaction commit failed. clusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), comitTrxErr)
		data.RollbackTrx(o)
		return comitTrxErr
	}
	//开启cron任务
	if err := scheduler.AddCronTask(infoSlice[0].BkStartTime, msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId()); err != nil {
		errMsg := fmt.Sprintf("AddCronTask failed,clusterId=[%v], nodeId=[%v], err=[%v]", msg.BaseMessage.ClusterId, err)
		common.Log.Error(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//处理每天日常的热备任务
func dealStartBackup(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	var (
		isFullDay         bool
		cycleStatus       int
		lsnFrom, remoteIp string
	)
	common.Log.Info("[dealStartBackup] start backup msg=[%+v].", *msg)
	//判断前一天的xtraBackup备份任务是否执行完成
	err, xtraTask := data.GetClusterLatestRecord(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId())
	if err != nil {
		errMsg := fmt.Sprintf("Failed to get xtra_task info from db. ClusterId=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//若上次的备份任务还没有完成 周期性探测上次备份任务是否结束 结束或等待超时后开启当天的备份任务
	if xtraTask != nil && isCurrentBackupIntermediate(xtraTask.BkExecStatus) {
		if err = checkXtraExecStatus(xtraTask.ClusterId, xtraTask.NodeId, xtraTask.TaskId); err != nil {
			errMsg := fmt.Sprintf("dealStartBackup failed. ClusterId=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}

	//根据集群id查询bk_task_info 判断是否为上线状态
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 || infoSlice[0].BkExecStatus != int32(common.BT_ONLINE) {
		errMsg := fmt.Sprintf("Failed to get bk_task info from db. ClusterId=[%v] err=[%v] BkExecStatus=[%v]",
			msg.GetBaseMessage().GetClusterId(), err, xtraTask.BkExecStatus)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//查询实例信息表获得备库实例信息
	backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
	if err != nil {
		errMsg := fmt.Sprintf("Failed to GetClusterInstance from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//根据备份类型和全备周期 获得当前时间执行的备份类型
	if xtraTask != nil {
		common.Log.Debug("[dealStartBackup] xtraTask=[%+v]", *xtraTask)
		// 若当前备份实例发生变化则进行全备
		if infoSlice[0].InstanceId != 0 && infoSlice[0].InstanceId != backupInstance.InstanceId {
			// 将备份实例ID信息更新至备份任务表中
			if err = data.UpdateBkTaskInstanceId(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId(), backupInstance.InstanceId); err != nil {
				return err
			}
			isFullDay = true
			cycleStatus = 0
			lsnFrom = "0"
			// lsn为空或者为初始lsn
		} else {
			// 对没有注册实例ID的将实例ID注册到对应的备份任务表
			if infoSlice[0].InstanceId == 0 {
				if err = data.UpdateBkTaskInstanceId(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId(), backupInstance.InstanceId); err != nil {
					return err
				}
			}
			if err, isFullDay, cycleStatus = calCurrentXbType(infoSlice[0].FullDay, int(infoSlice[0].BkCurrentCycle)); err != nil {
				errMsg := fmt.Sprintf("Failed to calCurrentXbType. ClusterId=[%v] err=[%v] BkExecStatus=[%v]",
					msg.GetBaseMessage().GetClusterId(), err, infoSlice[0].BkExecStatus)
				common.Log.Warn(errMsg)
				return errors.New(errMsg)
			}
			lsnFrom = infoSlice[0].BkLsnPos
		}
	} else {
		// 将备份实例ID信息注册到备份任务表
		if err = data.UpdateBkTaskInstanceId(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId(), backupInstance.InstanceId); err != nil {
			return err
		}
		//集群第一次进行备份
		isFullDay = true
		cycleStatus = 0
		lsnFrom = "0"
	}

	//选择空闲余量最大的大磁盘机器
	if remoteIp, _ = getRemoteMachine(common.BACKUP, global.DbsEnv(infoSlice[0].Env)); remoteIp == "" {
		errMsg := fmt.Sprintf("Failed to getRemoteMachine from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//插入一条xtra record
	newXtraRecord, err := data.AddXtraTaskRecord(backupInstance.Ip, remoteIp, infoSlice[0], lsnFrom, isFullDay, cycleStatus)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to insert xtra record to db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//检查主备复制状态并获得备份账户密码
	err, xtraUser, xtraPassword := checkBkMysqlStatus(backupInstance.Ip, newXtraRecord.BkPath, backupInstance.MdcAgentPort)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to checkBkMysqlStatus from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		data.UpdateXtraTaskStatus(newXtraRecord.TaskId, pb_server.XtraTaskStatus_XtraExecFail, errMsg)
		return errors.New(errMsg)
	}

	//发送执行xtrabackup任务
	if err = execXtraTask(newXtraRecord, backupInstance.Ip, xtraUser, xtraPassword, backupInstance.MdcAgentPort); err != nil {
		errMsg := fmt.Sprintf("Failed to execXtraTask. ClusterId=[%v],err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		data.UpdateXtraTaskStatus(newXtraRecord.TaskId, pb_server.XtraTaskStatus_XtraExecFail, errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 上传到dbabos后状态或者失败状态 当天备份任务才算执行结束.
func isCurrentBackupEnd(bkExecStatus int32) bool {
	if bkExecStatus >= int32(pb_server.XtraTaskStatus_UploadDbaBosSuccess) ||
		bkExecStatus == int32(pb_server.XtraTaskStatus_XtraExecFail) ||
		bkExecStatus == int32(pb_server.XtraTaskStatus_UnpackFail) {
		return true
	}
	return false
}

// 上一天任务处于以下中间状态, 需要等待其状态转换完成或更新LSN后(上传华北BOS成功)再进行备份操作.
// TODO 优化此处逻辑, 从xtrabackup到上传BOS耗时较长, 大实例可能影响备份效率
func isCurrentBackupIntermediate(bkExecStatus int32) bool {
	_, ok := common.BackupIntermediateMap[pb_server.XtraTaskStatus(bkExecStatus)]
	return ok
}

//检查上一次的xtra备份任务是否完成
func checkXtraExecStatus(clustreId int64, nodeId int64, taskId int64) error {
	tick := time.NewTicker(time.Second * time.Duration(common.Config.MdcServerMonitorCronStep))
	common.Log.Notice("start checkXtraExecStatus. clusteId=[%v] nodeId=[%v] taskId=[%v]", clustreId, nodeId, taskId)
	timeOut := time.After(time.Hour * time.Duration(common.Config.MdcServerMonitorCronTimeout))
	for {
		select {
		case <-tick.C:
			//判断xtra task任务状态
			err, xtraTask := data.GetXtraTaskInfo(clustreId, nodeId, taskId)
			if err != nil || xtraTask == nil {
				common.Log.Notice("GetXtraTaskInfo failed,err=[%v],xtraTask=[%v]", err, xtraTask)
			} else {
				// 1.如果备份状态为bos上传之后的状态
				// 2.如果备份状态为备份执行失败
				// 3.备份状态为打包失败
				// 以上三个条件满足任意一个，可以继续执行
				if isCurrentBackupEnd(xtraTask.BkExecStatus) {
					return nil
				}
			}
		case <-timeOut:
			errMsg := fmt.Sprintf("checkXtraExecStatus timeout. cluster_id=[%v] node_id=[%v] task_id=[%v]", clustreId, nodeId, taskId)
			common.Log.Warn(errMsg)

			// 将超时的备份状态更改为错误状态
			_, err := dt.RawExecSql(nil, "update bk_xtratask_exec_record set bk_exec_status = ？ where task_id = ? and cluster_id = ? and node_id = ?",
				pb_server.XtraTaskStatus_XtraTaskAbandon, taskId, clustreId, nodeId)
			if err != nil {
				common.Log.Warn("failed to update bk_exec_status info. TaskId=[%v] err=[%v]", taskId, err)
				return err
			}
			return nil
		}
	}
}

//计算当前时间的备份类型
func calCurrentXbType(bkFullDay int32, lastXkCycleStatus int) (err error, isFullBk bool, xbCycleStatus int) {
	weekDay := time.Now().Weekday().String()
	idx, ok := common.BkDay_Value[weekDay]
	if !ok {
		errMsg := fmt.Sprintf("calCurrentXbType,Get weekDay failed,weekday=[%v]", weekDay)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), false, -1
	}
	//当前时间为全备周期或大于第6个备份日
	if idx == bkFullDay || lastXkCycleStatus >= 6 {
		return nil, true, 0
	}
	//当前时间为增备周期
	lastXkCycleStatus++
	return nil, false, lastXkCycleStatus
}

//获取磁盘空闲余量最大的机器
func getRemoteMachine(mType common.MachineType, env global.DbsEnv) (machineIp, workDir string) {
	machineInfoList, err := data.GetMachineList(mType, env)
	if err != nil || len(machineInfoList) == 0 {
		errMsg := fmt.Sprintf("Get machineInfoList failed,err=[%v],len(machineInfoList)=[%v]", err, machineInfoList)
		common.Log.Warn(errMsg)
		return "", ""
	}
	// 可选用机器集合
	bigDiskMachineList := make([]string, 0)
	bigDiskWorkDirList := make([]string, 0)
	for _, mInfo := range machineInfoList {
		//构建rpc请求获得机器余量
		bkReq := pb_agent.MdcAgentSyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_GET_FREE_CAP,
			MsgType: &pb_agent.MdcAgentSyncMsg_GetFreeStorage{
				GetFreeStorage: &pb_agent.GetFreeStorage{
					DataDir: common.RemoteBaseDir,
				},
			},
		}
		// 获取发起rpc任务的rpc地址及执行任务的agentIP
		// 获取大磁盘空间人任务走默认端口
		agentAddr := fmt.Sprintf("%s:%d", mInfo.MachineIp, common.Config.AgentPort)
		if bkRes, err := communicator.SendSyncMsgToDagent(bkReq, agentAddr); err == nil {
			freeCap := bkRes.GetGetFreeStorageRes().GetFreeCapacity()
			// 最少有1T空间纳入可选机器范围
			if freeCap > common.DISK_LOWEST_LEVEL {
				//// 获取当前天大磁盘机器已承接的备份任务数量
				//hitNum, err := data.GegBigDiskMachine(mInfo.MachineIp)
				//if err != nil {
				//	common.Log.Warn("get bigDisk machine hit num failed, instanceIP=[%v] err=[%v]", mInfo.MachineIp, err)
				//	continue
				//}
				//if hitNum >= common.Config.BigDiskHitNum {
				//	common.Log.Warn("the number of backup jobs exceeded the upper limit, instance_ip=[%v] standardNum=[%v] currentNum=[%v]",
				//		mInfo.MachineIp, common.Config.BigDiskHitNum, hitNum)
				//	continue
				//}
				bigDiskMachineList = append(bigDiskMachineList, mInfo.MachineIp)
				if mInfo.WorkDir == "" {
					mInfo.WorkDir = common.RsPrefixPathName
				}
				bigDiskWorkDirList = append(bigDiskWorkDirList, mInfo.WorkDir)
			}
		} else {
			common.Log.Notice("getRemoteMachine failed,err=[%v],instanceInfo=[%v],agentAddr=[%v]", err, mInfo, agentAddr)
		}
	}
	if len(bigDiskMachineList) == 0 {
		return "", ""
	}

	targetNum := getMachineIndex(env, int64(len(bigDiskMachineList)))

	return bigDiskMachineList[targetNum], bigDiskWorkDirList[targetNum]
}

//检查主备状态并获得账户密码
func checkBkMysqlStatus(agentIp string, bkPath string, agentPort int64) (error, string, string) {
	bkReq := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CHECK_STATUS,
		MsgType: &pb_agent.MdcAgentSyncMsg_CheckBkmysqlStatus{
			CheckBkmysqlStatus: &pb_agent.CheckBkMysqlStatus{
				BkIp:   agentIp,
				BkPath: bkPath,
			},
		},
	}

	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// 检查集群备库主从状态任务在备库实例
	agentAddr := fmt.Sprintf("%s:%d", agentIp, agentPort)
	bkRes, err := communicator.SendSyncMsgToDagent(bkReq, agentAddr)
	if err != nil {
		errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", ""
	}
	//获取检查结果
	ioThread := bkRes.GetCheckBkMysqlStatusRes().GetIoThread()
	sqlThread := bkRes.GetCheckBkMysqlStatusRes().GetSqlThread()
	if ioThread && sqlThread {
		return nil, bkRes.GetCheckBkMysqlStatusRes().XtraUser, bkRes.GetCheckBkMysqlStatusRes().XtraPassword
	} else {
		errMsg := fmt.Sprintf("checkBkMysqlStatus failed,ioThread=[%v],sqlThread=[%v]", ioThread, sqlThread)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), "", ""
	}
	return nil, "", ""
}

//向mdc-agent发送exec xtra  task，并修改表中任务的状态
func execXtraTask(xtraTask *dao_mdc.BkXtraTaskExecRecord, agentIp, xtraUser, xtraPasword string, agentPort int64) error {
	common.Log.Info("[execXtraTask] exec backup task xtraTask=[%+v] agentIp=[%v] xtraUser=[%v] xtraPassword=[%v] agentPort=[%v]",
		&xtraTask, agentIp, xtraUser, xtraPasword, agentPort)
	bkReq := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_START_XTRA,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   xtraTask.ClusterId,
			NodeId:      xtraTask.NodeId,
			ClusterName: xtraTask.ClusterName,
		},
		TaskId: xtraTask.TaskId,
		MsgType: &pb_agent.MdcAgentAsyncMsg_StartXtrabkExecute{
			StartXtrabkExecute: &pb_agent.StartXtrabkExecute{
				BkBasedir:  xtraTask.BkPath,
				BkUser:     xtraUser,
				BkPassword: xtraPasword,
				BkLsnPos:   xtraTask.LsnFrom,
				RemoteIp:   xtraTask.BkRemoteIp,
				RemotePath: xtraTask.BkRemotePath[0:strings.LastIndex(xtraTask.BkRemotePath, ".")],
				Bkopt:      xtraTask.BkOpt,
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	agentAddr := fmt.Sprintf("%s:%d", agentIp, agentPort)
	if _, err := communicator.SendAsyncMsgToDagent(bkReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, bkReq, agentAddr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//修改任务状态并添加日志记录
	logStr := fmt.Sprintf("mdc-server send msg to mdc-agent，msgType=[%v],start exec xtrabackup", bkReq.MdcAgentTaskType)
	if err := data.UpdateXtraTaskStatus(xtraTask.TaskId, pb_server.XtraTaskStatus_XtraExecuting, logStr); err != nil {
		errMsg := fmt.Sprintf("execXtraTask failed due to UpdateXtraTaskStatus,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//处理关闭xtra备份任务
func dealStopBackup(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	// 开启事务
	o, beginTrxErr := data.BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Failed to start transaction before dealStopBackup . ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), beginTrxErr)
		return beginTrxErr
	}
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		data.RollbackTrx(o)
		return err
	}
	common.Log.Notice("update bktask status,bktask=[%v]", infoSlice[0])
	//判断当前任务为上线状态
	if infoSlice[0].BkExecStatus == int32(common.BS_ONLINE) {
		//判断前一天的xtraBackup备份任务是否执行完成
		err, xtraTask := data.GetClusterLatestRecord(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId())
		if err != nil {
			common.Log.Warn("Failed GetClusterLatestRecord from db. ClusterId=[%v] err=[%v]",
				msg.GetBaseMessage().GetClusterId(), err)
			data.RollbackTrx(o)
			return err
		}
		common.Log.Notice("update bktask status,bktask=[%v]，xtrtask=[%v]", infoSlice[0], xtraTask)
		if xtraTask != nil && xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_XtraExecuting) {
			//发送关闭备份任务给mdc-agent
			bkReq := pb_agent.MdcAgentSyncMsg{
				MdcAgentTaskType: pb_server.MdcAgentTaskType_STOP_XTRA,
				MsgType: &pb_agent.MdcAgentSyncMsg_StopXtrabkExecute{
					StopXtrabkExecute: &pb_agent.StopXtrabkExecute{},
				},
			}
			//查询实例获得备库实例机器IP
			backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
			if err != nil {
				common.Log.Warn("Failed to get backup instance info from db. ClusterId=[%v] err=[%v]",
					msg.GetBaseMessage().GetClusterId(), err)
				data.RollbackTrx(o)
				return err
			}
			// 获取发起rpc任务的rpc地址及执行任务的agentIP
			agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
			_, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
			if err != nil {
				errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
				common.Log.Warn(errMsg)
				data.RollbackTrx(o)
				return errors.New(errMsg)
			}
		}
		//将该任务对应的cron定时任务从定时器中移除
		err = scheduler.DelCronTask(msg.BaseMessage.ClusterId, msg.BaseMessage.NodeId)
		if err != nil {
			errMsg := fmt.Sprintf("exec DelCronTask failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			data.RollbackTrx(o)
			return errors.New(errMsg)
		}
	}
	common.Log.Notice("update bktask status,bktask=[%v]", infoSlice[0])
	//修改备份任务信息表中的任务状态为下线状态
	infoSlice[0].BkExecStatus = int32(common.BS_OFFLINE)
	conds := []string{"ClusterId", "NodeId"}
	cols := []string{"BkExecStatus"}
	effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
	if nil != err || effectRows != 1 {
		errStr := fmt.Sprintf("Fail to dealStopBackup due to update bkTaskInfo failed. clusterId=[%v]"+
			" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
		common.Log.Error(errStr)
		data.RollbackTrx(o)
		return errors.New(errStr)
	}
	// 如果状态修改成功，提交事务
	comitTrxErr := data.CommitTrx(o)
	if comitTrxErr != nil {
		common.Log.Warn("Update bkTask_status is finish ! Transaction commit failed. clusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), comitTrxErr)
		data.RollbackTrx(o)
		return comitTrxErr
	}
	return nil
}

//处理删除xtra备份任务
func dealDelBackup(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	// 开启事务
	o, beginTrxErr := data.BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Failed to start transaction before dealStopBackup . ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), beginTrxErr)
		return beginTrxErr
	}
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		data.RollbackTrx(o)
		return err
	}
	//判断当前任务为上线状态
	if infoSlice[0].BkExecStatus == int32(common.BS_ONLINE) {
		//判断前一天的xtraBackup备份任务是否执行完成
		err, xtraTask := data.GetClusterLatestRecord(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId())
		if err != nil {
			common.Log.Warn("Failed GetClusterLatestRecord from db. ClusterId=[%v] err=[%v]",
				msg.GetBaseMessage().GetClusterId(), err)
			data.RollbackTrx(o)
			return err
		}
		if xtraTask != nil && xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_XtraExecuting) {
			//发送关闭备份任务给mdc-agent
			bkReq := pb_agent.MdcAgentSyncMsg{
				MdcAgentTaskType: pb_server.MdcAgentTaskType_STOP_XTRA,
				MsgType: &pb_agent.MdcAgentSyncMsg_StopXtrabkExecute{
					StopXtrabkExecute: &pb_agent.StopXtrabkExecute{},
				},
			}
			//查询实例获得备库实例机器IP
			backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
			if err != nil {
				common.Log.Warn("Failed to get backup instance info from db. ClusterId=[%v] err=[%v]",
					msg.GetBaseMessage().GetClusterId(), err)
				data.RollbackTrx(o)
				return err
			}
			// 获取发起rpc任务的rpc地址及执行任务的agentIP
			agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
			_, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
			if err != nil {
				errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
				common.Log.Warn(errMsg)
				data.RollbackTrx(o)
				return errors.New(errMsg)
			}
		}
		//将该任务对应的cron定时任务从定时器中移除
		err = scheduler.DelCronTask(msg.BaseMessage.ClusterId, msg.BaseMessage.NodeId)
		if err != nil {
			errMsg := fmt.Sprintf("exec DelCronTask failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			data.RollbackTrx(o)
			return errors.New(errMsg)
		}
	}
	//删除备份任务
	conds := []string{"ClusterId", "NodeId"}
	effectRows, err := infoSlice[0].DeleteByCondCols(conds)
	if nil != err || effectRows != 1 {
		errStr := fmt.Sprintf("Fail to dealDelBackup due to delete bkTaskInfo failed. clusterId=[%v]"+
			" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
		common.Log.Error(errStr)
		data.RollbackTrx(o)
		return errors.New(errStr)
	}
	// 如果任务删除成功，提交事务
	comitTrxErr := data.CommitTrx(o)
	if comitTrxErr != nil {
		common.Log.Warn("Delete bkTask is finish ! Transaction commit failed. clusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), comitTrxErr)
		data.RollbackTrx(o)
		return comitTrxErr
	}
	return nil
}

//处理开启binlog备份
func dealStartBinlog(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	// 开启事务
	o, beginTrxErr := data.BeginTrx()
	if beginTrxErr != nil {
		common.Log.Warn("Failed to start transaction before dealStartBinlog . ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), beginTrxErr)
		return beginTrxErr
	}
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		errMsg := fmt.Sprintf("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		common.Log.Warn(errMsg)
		data.RollbackTrx(o)
		return errors.New(errMsg)
	}

	bkRes := new(pb_agent.MdcAgentSyncRespons)
	//修改备份任务信息表中任务状态
	if infoSlice[0].BinlogProcessStatus != int32(common.BT_ONLINE) {
		//查询实例表表获得备库库实例机器IP
		backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
		if err != nil {
			errMsg := fmt.Sprintf("Failed to get back instance info from db. ClusterId=[%v] err=[%v]",
				msg.GetBaseMessage().GetClusterId(), err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}

		if infoSlice[0].BinlogBkDir == "" {
			//更新备份信息表中备库实例上备份binlog目录
			infoSlice[0].BinlogBkDir = fmt.Sprintf("%v/%v_%v_%v", common.BkBinlogBaseDir, infoSlice[0].ClusterName, bkInfo.ClusterId, bkInfo.NodeId)
			conds := []string{"ClusterId", "NodeId"}
			cols := []string{"BinlogBkDir"}
			effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
			if nil != err || effectRows != 1 {
				errStr := fmt.Sprintf("Fail to dealStartBinlog due to update bkTaskInfo failed. clusterId=[%v]"+
					" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
				common.Log.Error(errStr)
				data.RollbackTrx(o)
				return errors.New(errStr)
			}
		}
		//发送开启binlog同步任务给mdc-agent
		bkReq := pb_agent.MdcAgentSyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_START_BINLOG,
			MsgType: &pb_agent.MdcAgentSyncMsg_ExecStartBinlog{
				ExecStartBinlog: &pb_agent.ExecStartBinlog{
					BkMysqlPath: infoSlice[0].BkMysqlBasedir,
					BkBinlogDir: infoSlice[0].BinlogBkDir,
				},
			},
		}
		// 获取发起rpc任务的rpc地址及执行任务的agentIP
		agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
		bkRes, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
		if err != nil {
			errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
			common.Log.Warn(errMsg)
			data.RollbackTrx(o)
			return errors.New(errMsg)
		}
		//根据mdc-agent的回复更新bkTaskInfo表中binlog进程Id
		infoSlice[0].BinlogServerId = bkRes.GetStartBinlogRes().GetBinlogServerId()
		infoSlice[0].BinlogProcessStatus = int32(common.BT_ONLINE)
		infoSlice[0].BinlogCheckStatus = int32(pb_server.BinlogCheckStatus_CheckInit)
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BinlogServerId", "BinlogProcessStatus", "BinlogCheckStatus"}
		effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
		if nil != err || effectRows != 1 {
			errStr := fmt.Sprintf("Fail to dealStartBinlog due to update bkTaskInfo failed. clusterId=[%v]"+
				" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
			common.Log.Error(errStr)
			return errors.New(errStr)
		}
		// 如果状态修改成功，提交事务
		comitTrxErr := data.CommitTrx(o)
		if comitTrxErr != nil {
			errMsg := fmt.Sprintf("Update bkTask_status is finish ! Transaction commit failed. clusterId=[%v] err=[%v]",
				msg.GetBaseMessage().GetClusterId(), comitTrxErr)
			common.Log.Warn(errMsg)
			data.RollbackTrx(o)
			return errors.New(errMsg)
		}
	}

	return nil
}

//处理关闭binlog备份
func dealStopBinlog(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	// 开启事务
	o, beginTrxErr := data.BeginTrx()
	if beginTrxErr != nil {
		errMsg := fmt.Sprintf("Failed to start transaction before dealStopBinlog . ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), beginTrxErr)
		common.Log.Warn(errMsg)
		return beginTrxErr
	}
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		data.RollbackTrx(o)
		return err
	}
	//修改备份任务信息表中任务状态
	if infoSlice[0].BinlogProcessStatus != int32(common.BT_OFFLINE) {
		//查询实例信息表获得备库库实例机器IP
		backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
		if err != nil {
			common.Log.Warn("Failed to get back instance info from db. ClusterId=[%v] err=[%v]",
				msg.GetBaseMessage().GetClusterId(), err)
			data.RollbackTrx(o)
			return err
		}
		//发送关闭binlog备份任务给mdc-agent
		bkReq := pb_agent.MdcAgentSyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_STOP_BINLOG,
			MsgType: &pb_agent.MdcAgentSyncMsg_ExecStopBinlog{
				ExecStopBinlog: &pb_agent.ExecStopBinlog{
					ServerId: infoSlice[0].BinlogServerId,
				},
			},
		}
		// 获取发起rpc任务的rpc地址及执行任务的agentIP
		agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
		// TODO: fix  该场景在更换机器场景下会下线报错，因为该机器上没有这个binlog进程。
		_, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
		if err != nil {
			errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
			common.Log.Warn(errMsg)
		}
	}
	//修改binlog备份任务状态为下线状态
	infoSlice[0].BinlogProcessStatus = int32(common.BT_OFFLINE)
	infoSlice[0].BinlogServerId = common.BT_OFFLINE_SERVERID
	infoSlice[0].BinlogCheckStatus = int32(pb_server.BinlogCheckStatus_CheckInit)
	conds := []string{"ClusterId", "NodeId"}
	cols := []string{"BinlogProcessStatus", "BinlogServerId", "BinlogCheckStatus"}
	effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
	if nil != err || effectRows != 1 {
		errStr := fmt.Sprintf("Fail to dealStopBinlog due to update bkTaskInfo failed. clusterId=[%v]"+
			" effectRows=[%v] err=[%v]", msg.GetBaseMessage().GetClusterId(), effectRows, err)
		common.Log.Error(errStr)
		data.RollbackTrx(o)
		return errors.New(errStr)
	}
	// 如果状态修改成功，提交事务
	comitTrxErr := data.CommitTrx(o)
	if comitTrxErr != nil {
		common.Log.Warn("Update bkTask_status is finish ! Transaction commit failed. clusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), comitTrxErr)
		data.RollbackTrx(o)
		return comitTrxErr
	}
	return nil
}

//处理binlog备份定时探测任务
func dealBinlogDetect(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	//根据集群id查询bk_task_info
	bkInfo := new(dao_mdc.BkTaskInfo)
	bkInfo.ClusterId = msg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = msg.GetBaseMessage().NodeId
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		return err
	}
	// 判断备份任务信息表中binlog任务状态 如果是下线状态 跳过该次探测
	if infoSlice[0].BinlogProcessStatus == int32(common.BT_OFFLINE) {
		common.Log.Notice("binlog task is offline,clusterId =[%v]", infoSlice[0].ClusterId)
		return nil
	}
	// 查询实例信息表获得备库库实例机器IP
	backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, msg.GetBaseMessage().GetIsFdb())
	if err != nil {
		common.Log.Warn("Failed to get back instance info from db. ClusterId=[%v] err=[%v]",
			msg.GetBaseMessage().GetClusterId(), err)
		return err
	}
	var remoteIp, remotePath string
	// 若大磁盘实例不可用
	// TODO： 若将binlog均分到所有机器，考虑到机器故障后，可能会导致所有集群binlog均有缺失，故不将binlog进行随机机器分配，而是指定到某一台机器上，后续物理备份逻辑也考虑这样实现。
	if !data.IsBigDiskMachineAvailable(infoSlice[0].BkRemoteMachine) {
		// 选择空闲余量最大的大磁盘机器
		remoteIp, _ = getRemoteMachine(common.BACKUP, global.DbsEnv(infoSlice[0].Env))
		remotePath = fmt.Sprintf("%v/%v_%v_%v", common.RemoteBinlogDir, infoSlice[0].ClusterName, infoSlice[0].ClusterId, infoSlice[0].NodeId)
		// 更新信息
		infoSlice[0].BkRemoteMachine = remoteIp
		if _, err = infoSlice[0].UpdateById([]string{"BkRemoteMachine"}); err != nil {
			common.Log.Error("can't update BkRemoteMachine to bk_task_info. infoSlice=[%v]", infoSlice[0])
		}
	} else {
		remoteIp = infoSlice[0].BkRemoteMachine
		remotePath = fmt.Sprintf("%v/%v_%v_%v", common.RemoteBinlogDir, infoSlice[0].ClusterName, infoSlice[0].ClusterId, infoSlice[0].NodeId)
	}

	// 修改当前集群binlog备份检查任务状态为正在检查中
	err = data.UpdateCheckBinlogStatus(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId(), pb_server.BinlogCheckStatus_CheckIng)
	if err != nil {
		errMsg := fmt.Sprintf("Description Failed to check the binlog backup job, err=[%v]", err)
		common.Log.Warn(errMsg)
		return err
	}

	//发送检查binlog备份状态异步任务给mdc-agent
	bkReq := pb_agent.MdcAgentAsyncMsg{
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId: infoSlice[0].ClusterId,
			NodeId:    infoSlice[0].NodeId,
		},
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CHECK_BINLOGSTATUS,
		MsgType: &pb_agent.MdcAgentAsyncMsg_CheckBinlogProcessStatus{
			CheckBinlogProcessStatus: &pb_agent.CheckBinlogProcessStatus{
				ServerId:      infoSlice[0].BinlogServerId,
				RemoteIp:      remoteIp,
				RemotePath:    remotePath,
				BkMysqlPath:   infoSlice[0].BkMysqlBasedir,
				BkBinlogDir:   infoSlice[0].BinlogBkDir,
				EndUpdateTime: time.Now().Format(common.TIME_FORMAT),
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
	_, err = communicator.SendAsyncMsgToDagent(bkReq, agentAddr)
	if err != nil {
		// 修改当前集群binlog备份检查任务状态修改为检查失败
		err = data.UpdateCheckBinlogStatus(msg.GetBaseMessage().GetClusterId(), msg.GetBaseMessage().GetNodeId(), pb_server.BinlogCheckStatus_CheckFail)
		if err != nil {
			errMsg := fmt.Sprintf("Description Failed to check the binlog backup job, err=[%v]", err)
			common.Log.Warn(errMsg)
			return err
		}
		errMsg := fmt.Sprintf("SendAsyncMsgToMdcAgent failed, err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

//处理数据清理任务
func dealCleanBRData(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	if msg.GetCleanDataTaskReqMsg().GetDataDir() == nil || len(msg.GetCleanDataTaskReqMsg().GetDataDir()) == 0 {
		errMsg := fmt.Sprintf("dealCleanBRData failed,Invalid msg,msg=[%v]", msg)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//发送清理任务给mdc-agent
	bkReq := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CLEAN_DATA,
		MsgType: &pb_agent.MdcAgentSyncMsg_CleanData{
			CleanData: &pb_agent.CleanData{
				DataDir:       msg.GetCleanDataTaskReqMsg().GetDataDir(),
				CleanDataType: msg.GetCleanDataTaskReqMsg().GetCleanDataType(),
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// 数据清理任务都是在大磁盘处理,默认使用mdc-agent端口
	agentAddr := fmt.Sprintf("%s:%d", msg.GetCleanDataTaskReqMsg().GetInstanceIp(), common.Config.AgentPort)
	_, err := communicator.SendSyncMsgToDagent(bkReq, agentAddr)
	if err != nil {
		errMsg := fmt.Sprintf("dealCleanBRData failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	cleanDataType := msg.GetCleanDataTaskReqMsg().GetCleanDataType()
	if cleanDataType == pb_server.DataType_BK_REMOTE_DATA {
		logStr := fmt.Sprintf("mdc-server clean remote backup data successful")
		if err := data.UpdateXtraTaskStatus(msg.GetCleanDataTaskReqMsg().GetTaskId(), pb_server.XtraTaskStatus_RemoteBkDataClean, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
	} else if cleanDataType == pb_server.DataType_BOS_DATA {
		logStr := fmt.Sprintf("mdc-server clean dba bos data successful ")
		if err := data.UpdateXtraTaskStatus(msg.GetCleanDataTaskReqMsg().GetTaskId(), pb_server.XtraTaskStatus_DbaBosDataClean, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
	} else if cleanDataType == pb_server.DataType_RS_REMOTE_DATA {
		logStr := fmt.Sprintf("mdc-server clean restore data successful")
		if err := data.UpdateRsTaskStatus(msg.GetCleanDataTaskReqMsg().GetTaskId(), pb_server.RsTaskStatus_RsDataClean, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
	} else {
		common.Log.Notice("dealCleanBRData=[%v]. cleanDataType=[%v]")
	}
	return nil
}

//处理发起补备任务
func dealReTryBackup(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	//根据任务id和集群id查找xtra任务
	clusterId := msg.GetBaseMessage().GetClusterId()
	nodeId := msg.GetBaseMessage().GetNodeId()
	taskId := msg.GetRetryBackupTaskReqMsg().GetTaskId()
	err, xtraTask := data.GetXtraTaskInfo(clusterId, nodeId, taskId)
	if err != nil || xtraTask == nil {
		errMsg := fmt.Sprintf("dealReTryBackup failed,err=[%v],xtraTask=[%v]", err, xtraTask)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	switch xtraTask.BkExecStatus {
	case int32(pb_server.XtraTaskStatus_InitStatus), int32(pb_server.XtraTaskStatus_XtraExecFail), int32(pb_server.XtraTaskStatus_UnpackFail):
		//修改失败任务的状态为终止状态
		logStr := fmt.Sprintf("xtra task termination,end with status=[%v]", xtraTask.BkExecStatus)
		if err := data.UpdateXtraTaskStatus(xtraTask.TaskId, pb_server.XtraTaskStatus_XtraTaskAbandon, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
		//开启新的备份任务
		scheduler.StartBackupTask(clusterId, nodeId)
	case int32(pb_server.XtraTaskStatus_UploadDbaBosFail):
		bkTask := &pb_server.MdcAgentReportMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_UNPACK_DATA,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   clusterId,
				ClusterName: msg.GetBaseMessage().GetClusterName(),
				NodeId:      msg.GetBaseMessage().GetNodeId(),
			},
			Taskid:   taskId,
			ClientIp: xtraTask.BkRemoteIp,
			MsgType: &pb_server.MdcAgentReportMsg_ExecXtrabkReport{
				ExecXtrabkReport: &pb_server.ExecXtrabkReport{
					XtraEndTime: xtraTask.BkEndTime,
					BkLsnFrom:   xtraTask.LsnFrom,
					BkLsnTo:     xtraTask.LsnTo,
					BkSize:      xtraTask.BkSize,
				},
			},
		}
		err = communicator.PutMsMsgToChan(*bkTask)
		if err != nil {
			common.Log.Warning("ErrorMsg=[Tran Msg to task failed].CalledError=[%v]", err)
			return err
		}
	case int32(pb_server.XtraTaskStatus_UploadSiodBosFail):
		bkTask := &pb_server.MdcAgentReportMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_UPLOADDBABOS,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   clusterId,
				ClusterName: msg.GetBaseMessage().GetClusterName(),
				NodeId:      msg.GetBaseMessage().GetNodeId(),
			},
			Taskid:   taskId,
			ClientIp: xtraTask.BkRemoteIp,
			MsgType: &pb_server.MdcAgentReportMsg_UploadDBABOSReport{
				UploadDBABOSReport: &pb_server.UploadDBABOSReport{},
			},
		}
		err = communicator.PutMsMsgToChan(*bkTask)
		if err != nil {
			common.Log.Warning("ErrorMsg=[Tran Msg to task failed].CalledError=[%v]", err)
		}
	default:
		common.Log.Warning("Unknown ReTryBackup task status type =[%v]", xtraTask.BkExecStatus)
		return err
	}
	return nil
}

// 处理授权逻辑
func dealGrantPrivileges(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	var (
		agentPort int32
		config    global.GrantPrivilegesConfig
	)
	configStr := msg.GetGrantPrivileges().GrantPrivilegesConfig

	err := json.Unmarshal([]byte(configStr), &config)
	if err != nil {
		return err
	}
	//发送授权任务给mdc-agent
	bkReq := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_GRANT_INSTANCE_PRIVILEGES,
		MsgType: &pb_agent.MdcAgentSyncMsg_GrantPrivileges{
			GrantPrivileges: &pb_server.GrantPrivileges{
				GrantPrivilegesConfig: configStr,
			},
		},
	}
	if config.MdcAgentPort != 0 {
		agentPort = config.MdcAgentPort
	} else {
		agentPort = int32(common.Config.AgentPort)
	}
	agentAddr := fmt.Sprintf("%s:%d", config.InstanceIp, agentPort)
	_, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
	if err != nil {
		errMsg := fmt.Sprintf("dealGrantPrivileges failed. err=[%v] req=[%+v]", err, bkReq)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}
