package worker

import (
	"testing"

	dao_mdc "dt-common/dao/mdc"
	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

func init() {
	common.ParseConfig("../conf/mdc-server.yaml")
	data.InitDao()
}

func TestDealXwebStartBackup(t *testing.T) {
	msg := &pb_server.XwebPlusFrontAsyncMsg{
		TaskType: pb_server.MdcServerTaskType_FRONT_START_BACKUP,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterName: "test",
			ClusterId:   193827,
			NodeId:      1,
		},
	}
	Convey("测试dealXwebStartBackup", t, func() {
		err := dealXwebStartBackup(msg)
		So(err, ShouldBeNil)
	})
}

func TestDealStartBackup(t *testing.T) {
	Convey("测试dealStartBackup", t, func() {
		msg := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_FRONT_START_BACKUP,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		err := dealStartBackup(msg)
		So(err, ShouldBeNil)
	})

	Convey("测试dealStartBackup", t, func() {
		msg := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_FRONT_START_BACKUP,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   19,
				NodeId:      1,
			},
		}

		err := dealStartBackup(msg)
		So(err, ShouldBeNil)
	})

}

func TestCheckXtraExecStatus(t *testing.T) {
	common.Config.MdcServerMonitorCronStep = 1
	err := checkXtraExecStatus(1, 1,1)
	So(err, ShouldBeNil)

}

func TestCalCurrentXbType(t *testing.T) {
	Convey("测试calCurrentXbType", t, func() {
		err, isFullBk, xbCycleStatus := calCurrentXbType(0, 0)
		So(isFullBk, ShouldBeFalse)
		So(xbCycleStatus, ShouldEqual, 1)
		So(err, ShouldBeNil)

		err, isFullBk, xbCycleStatus = calCurrentXbType(4, 2)
		So(isFullBk, ShouldBeTrue)
		So(xbCycleStatus, ShouldEqual, -1)
		So(err, ShouldBeNil)

	})
}

func TestGetRemoteMachine(t *testing.T) {
	Convey("测试getRemoteMachine", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		remoteIp, _ := getRemoteMachine(common.RESTORE)
		So(remoteIp, ShouldEqual, "")

		patches = ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			res := &pb_agent.MdcAgentSyncRespons{
				TaskId: 1,
				MsgType: &pb_agent.MdcAgentSyncRespons_GetFreeStorageRes{
					GetFreeStorageRes: &pb_agent.GetFreeStorageRes{
						FreeCapacity: 30,
					},
				},
			}
			return res, nil
		})
		defer patches.Reset()

		remoteIp, _= getRemoteMachine(common.BACKUP)
		So(remoteIp, ShouldEqual, "127.0.0.1")
	})
}

func TestCheckBkMysqlStatus(t *testing.T) {
	Convey("测试checkBkMysqlStatus", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		err, _, _ := checkBkMysqlStatus("127.0.0.1", "/home/<USER>/mysql")
		So(err, ShouldNotBeNil)
	})
}

func TestDealStopBackup(t *testing.T) {
	Convey("测试dealStopBackup", t, func() {
		msg1 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_FRONT_START_BACKUP,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_STOP_BACKUP,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		err := dealXwebStartBackup(msg1)
		So(err, ShouldBeNil)
		err = dealStopBackup(msg2)
		So(err, ShouldBeNil)
	})
}

func TestDealStartOrStopBinlog(t *testing.T) {
	Convey("测试DealStartOrStopBinlog", t, func() {
		msg1 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_START_BINLOG,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()

		bkInfo := new(dao_mdc.BkTaskInfo)
		bkInfo.ClusterId = 193827
		bkInfo.BinlogProcessStatus = int32(common.BS_OFFLINE)
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BinlogProcessStatus"}
		_, err := bkInfo.UpdateByCondCols(conds, cols)
		err = dealStartBinlog(msg1)
		So(err, ShouldBeNil)

		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_STOP_BINLOG,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		err = dealStopBinlog(msg2)
		So(err, ShouldBeNil)
	})
}

func TestDealBinlogDetect(t *testing.T) {
	Convey("测试dealBinlogDetect", t, func() {
		patches := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()

		bkInfo := new(dao_mdc.BkTaskInfo)
		bkInfo.ClusterId = 193827
		bkInfo.BinlogProcessStatus = int32(common.BS_OFFLINE)
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BinlogProcessStatus"}
		_, err := bkInfo.UpdateByCondCols(conds, cols)
		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_BINLOG_DETECT,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		err = dealBinlogDetect(msg2)
		So(err, ShouldBeNil)

	})

	Convey("测试dealBinlogDetect", t, func() {
		bkInfo := new(dao_mdc.BkTaskInfo)
		bkInfo.ClusterId = 193827
		bkInfo.BinlogProcessStatus = int32(common.BS_OFFLINE)
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BinlogProcessStatus"}
		_, err := bkInfo.UpdateByCondCols(conds, cols)
		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_BINLOG_DETECT,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		patches2 := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			res := &pb_agent.MdcAgentSyncRespons{
				TaskId: 1,
				MsgType: &pb_agent.MdcAgentSyncRespons_CheckBinlogStatusRes{
					CheckBinlogStatusRes: &pb_agent.CheckBinlogStatusRes{
						BinlogServerId: 234,
					},
				},
			}
			return res, nil
		})
		defer patches2.Reset()

		err = dealBinlogDetect(msg2)
		So(err, ShouldBeNil)

	})

}

func TestDealCleanBRData(t *testing.T) {
	Convey("测试dealCleanBRData", t, func() {
		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_CLEAN_BRDATA,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		patches2 := ApplyFunc(communicator.SendSyncMsgToDagent, func(data pb_agent.MdcAgentSyncMsg, clientIp string) (*pb_agent.MdcAgentSyncRespons, error) {
			return nil, nil
		})
		defer patches2.Reset()

		err := dealCleanBRData(msg2)
		So(err, ShouldBeNil)

	})
}

func TestDealReTryBackup(t *testing.T) {
	Convey("测试dealReTryBackup", t, func() {
		msg2 := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_RETRY_BACKUP,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}

		patches2 := ApplyFunc(data.GetXtraTaskInfo, func(clusterId int64, taskId int64) (error, *dao_mdc.BkXtraTaskExecRecord) {
			xtraTask := new(dao_mdc.BkXtraTaskExecRecord)
			xtraTask.ClusterId = 193827
			xtraTask.BkExecStatus = 3
			return nil, xtraTask
		})
		defer patches2.Reset()

		err := dealReTryBackup(msg2)
		So(err, ShouldBeNil)

	})
}
