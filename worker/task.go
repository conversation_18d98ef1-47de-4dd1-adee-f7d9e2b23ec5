package worker

import (
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	pb_server "dt-common/protobuf/mdc-server"

	"mdc-server/common"
	"mdc-server/data"
)

//开启工作协程
func StartWorker(workerNum int, chanTimeoutSeconds int, mWaitGroup *sync.WaitGroup) { //创建工作协程
	wg := new(sync.WaitGroup)
	for i := 0; i < workerNum; i++ {
		wg.Add(1)
		go workRoutine(workerNum, chanTimeoutSeconds, wg)
	}
	wg.Wait()
	mWaitGroup.Done()
}

//关闭工作协程
func StopWorker() { //退出工作协程
	if atomic.LoadInt32(&common.MsServerMsgChanIsClose) == 0 {
		atomic.AddInt32(&common.MsServerMsgChanIsClose, 1)
		//关闭全局Agent消息队列
		close(common.MsServerMsgChan)
	}
}

func workRoutine(num int, chanTimeoutSeconds int, wg *sync.WaitGroup) { //协程处理函数
	for {
		select {
		case msg, ok := <-common.MsServerMsgChan: //从channel取消息
			common.Log.Notice("get msg from MsServerMsgChan. msg=[%+v]", *msg)
			if ok {
				err := handleMsgChanTask(msg)
				if err != nil {
					common.Log.Warn("Failed to process message，err=[%v]", err)
				}
			} else {
				common.Log.Warn("The channel is closed,The routine exits normally")
				wg.Done()
				return
			}
		case <-time.After(time.Second * time.Duration(chanTimeoutSeconds)): //等待超时
			common.Log.Debug("worker[%d] routine wait timeout,current channel length=[%d],cap=[%d]", num, len(common.MsServerMsgChan), cap(common.MsServerMsgChan))
		}
	}
}

//处理任务队列中的各种任务
func handleMsgChanTask(message *common.MdcServerTaskInfo) error {
	var err error
	//ms角色认证，由master ms进行定时探测binlog进程
	role, err := data.CheckRole(common.Config.MdcServerId)
	if err != nil {
		common.Log.Warn("check ms role failed, err=[%v]", err)
		return err
	}
	//对ms身份进行判断
	switch role {
	case uint8(common.MS_SLAVE):
		return nil
	case uint8(common.MS_MASTER):
		//进行逻辑处理
		goto EXEC
	default:
		common.Log.Warn("unknown ms role=[%v]", role)
	}
EXEC:
	msg := message.MdcServerMsg
	// 根据任务发起方选择对应的函数
	switch msg.(type) {
	case pb_server.XwebPlusFrontAsyncMsg:
		//判断任务类型是否前端异步任务
		xwebMsg, ok := msg.(pb_server.XwebPlusFrontAsyncMsg)
		if !ok {
			err = errors.New(fmt.Sprintf("ErrorMsg=[fail to transform message.] "+
				"xwebMsg=[%v]", xwebMsg))
			common.Log.Warning(err.Error())
			return err
		}
		//处理任务
		if err = handleXwebFrontAsyncMsg(&xwebMsg); nil != err {
			common.Log.Warning("ErrorMsg=[fail to deal xweb front task.] CalledError=[%v] "+
				"taskType=[%v] clusterId=[%v]", err, xwebMsg.GetMsgType(),
				xwebMsg.GetBaseMessage().GetClusterId())
			return err
		}
	case pb_server.MdcAgentReportMsg:
		//判断任务类型是否mdc-agent汇报任务
		reportMsg, ok := msg.(pb_server.MdcAgentReportMsg)
		if !ok {
			err = errors.New(fmt.Sprintf("ErrorMsg=[fail to transform message.] "+
				"reportMsg=[%v]", reportMsg))
			common.Log.Warning(err.Error())
			return err
		}
		// 执行任务
		if err = handleAgentReportMsg(&reportMsg); nil != err {
			common.Log.Warning("ErrorMsg=[fail to deal mdc_agent report task.] CalledError=[%v] "+
				"taskType=[%v] clusterId=[%v]", err, reportMsg.GetMsgType(), reportMsg.GetBaseMessage().GetClusterId())
			return err
		}
	default:
		err = errors.New("Invalid message type")
		common.Log.Warning("ErrorMsg=[fail to transform agent message.] "+
			"CalledError=[%v] agentMsg=[%v]", err, msg)
	}
	return nil
}

//分类处理消息队列中的任务
func handleXwebFrontAsyncMsg(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	var err error
	common.Log.Notice("Start to do task.TaskType=[%v] clusterId=[%v]",
		msg.TaskType, msg.GetBaseMessage().GetClusterId())
	switch msg.TaskType {
	case pb_server.MdcServerTaskType_FRONT_START_BACKUP:
		err = dealXwebStartBackup(msg)
		if err != nil {
			common.Log.Warn("call dealXwebStartBackup failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_START_BACKUP:
		err = dealStartBackup(msg)
		if err != nil {
			common.Log.Warn("call dealStartBackup failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_STOP_BACKUP:
		err = dealStopBackup(msg)
		if err != nil {
			common.Log.Warn("call dealStopBackup failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_DEL_BACKUP:
		err = dealDelBackup(msg)
		if err != nil {
			common.Log.Warn("call dealDelBackup failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_START_BINLOG:
		err = dealStartBinlog(msg)
		if err != nil {
			common.Log.Warn("call dealStartBinlog failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_STOP_BINLOG:
		err = dealStopBinlog(msg)
		if err != nil {
			common.Log.Warn("call dealStopBinlog failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_RETRY_BACKUP:
		err = dealReTryBackup(msg)
		if err != nil {
			common.Log.Warn("call dealReTryBackup failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_RESTORE_DATA:
		err = dealRestoreData(msg)
		if err != nil {
			if errUpdate := data.UpdateRsTaskStatus(msg.GetRestoreDataTaskReqMsg().TaskId, pb_server.RsTaskStatus_ExecRestoreFail, err.Error()); errUpdate != nil {
				common.Log.Warn("UpdateRsTaskStatus failed,err=[%v]", errUpdate)
			}
			common.Log.Warn("call dealRestoreData failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_PARSE_BINLOG:
		err = dealParseBinlog(msg)
		if err != nil {
			common.Log.Warn("call dealParseBinlog failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_BINLOG_DETECT:
		err = dealBinlogDetect(msg)
		if err != nil {
			common.Log.Warn("call dealBinlogDetect failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_FRONT_CLEAN_BKDATA, pb_server.MdcServerTaskType_CLEAN_BRDATA:
		err = dealCleanBRData(msg)
		if err != nil {
			common.Log.Warn("call dealCleanBRData failed.err=[%v]", err)
			return err
		}
	case pb_server.MdcServerTaskType_GRANT_PRIVILEGES:
		err = dealGrantPrivileges(msg)
		if err != nil {
			common.Log.Warn("call dealGrantPrivileges failed.err=[%v]", err)
			return err
		}
	default:
		err = errors.New("invalid message type")
		common.Log.Warning("ErrorMsg=[fail to transform agent message.] "+
			"CalledError=[%v] agentMsg=[%v]", err, msg)
	}
	return nil
}

//处理mdc-agent的各类汇报任务
func handleAgentReportMsg(mdcAgentReportMsg *pb_server.MdcAgentReportMsg) error {
	var err error
	common.Log.Notice("[Mdc-Server] Received MdcAgentReportMsg successfully,TaskId=[%v], ClusterId=[%d], TaskType=[%v], ResultJson=[%s]",
		mdcAgentReportMsg.Taskid, mdcAgentReportMsg.GetBaseMessage().GetClusterId(), mdcAgentReportMsg.GetMsgType(), mdcAgentReportMsg.ErrorMsg)
	switch mdcAgentReportMsg.MdcAgentTaskType {
	case pb_server.MdcAgentTaskType_START_XTRA:
		if err = handleExecXtraReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleExecXtraReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_UNPACK_DATA:
		if err = handleUnpackDataReport(mdcAgentReportMsg); err != nil {
			common.Log.Warning("Failed to handleUnpackDataReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_UPLOADDBABOS:
		if err = handleUploadBosReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleUploadBosReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA:
		if err = handleDownloadDataReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleDownloadDataReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_DOWNLOAD_BINLOG_DATA:
		if err = handleDownloadBinlogDataReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleDownloadBinlogDataReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_EXEC_RESTORE, pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG:
		if err = handleExecRestoreReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleExecRestoreReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_CHECK_BINLOGSTATUS:
		if err = handleCheckBinlogReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("Failed to handleCheckBinlogReport, err=[%v]", err)
			return err
		}
	case pb_server.MdcAgentTaskType_EXEC_PARTITION_RESTORE:
		if err = handleRestorePartitionReport(mdcAgentReportMsg); err != nil {
			common.Log.Error("fail to  handleRestorePartitionReport. err=[%v] subTaskId=[%v]", err, mdcAgentReportMsg.Taskid)
		}
	default:
		err = errors.New("Invalid message type")
		common.Log.Warning("ErrorMsg=[fail to transform agent message.] "+
			"CalledError=[%v] agentMsg=[%v]", err, mdcAgentReportMsg)
	}
	return nil
}
