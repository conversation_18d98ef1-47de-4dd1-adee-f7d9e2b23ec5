package worker

import (
	"bytes"
	"dt-common/global"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	daoMdc "dt-common/dao/mdc"
	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	pb_tinker "dt-common/protobuf/tinker"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

// 用于存放邮件发送用户的账号信息
type AccountEmailInfo struct {
	DbUser      string
	DbPassword  string
	ClusterName string
	Address     string
	TaskId      int64
}

// 处理mdc-agent执行xtra的汇报任务
func handleExecXtraReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec xtrabackup fail report. err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent. msgType=[%v] errMsg=[%s]", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateXtraTaskStatus(agentReportMsg.GetTaskid(), pb_server.XtraTaskStatus_XtraExecFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}

	//执行成功 更新xtra备份相关信息
	bkTaskInfo := new(daoMdc.BkXtraTaskExecRecord)
	bkTaskInfo.TaskId = agentReportMsg.Taskid
	bkTaskInfo.BkSize = fmt.Sprintf("%v", agentReportMsg.GetExecXtrabkReport().BkSize)
	bkTaskInfo.LsnFrom = agentReportMsg.GetExecXtrabkReport().BkLsnFrom
	bkTaskInfo.LsnTo = agentReportMsg.GetExecXtrabkReport().BkLsnTo
	bkTaskInfo.BkEndTime = agentReportMsg.GetExecXtrabkReport().XtraEndTime
	//需要更新的列
	conds := []string{"TaskId"}
	cols := []string{"BkSize", "LsnFrom", "LsnTo", "BkEndTime"}
	// 若汇报中包含远程目录文件路径，则进行更新
	if strings.Contains(agentReportMsg.GetExecXtrabkReport().GetRemotePathFile(), common.XbstreamType) {
		bkTaskInfo.BkRemotePath = agentReportMsg.GetExecXtrabkReport().GetRemotePathFile()
		cols = append(cols, "BkRemotePath")
	}
	effectRows, err := bkTaskInfo.UpdateByCondCols(conds, cols)
	if nil != err {
		errStr := fmt.Sprintf("Fail to handleExecXtraReport due to update bkTaskInfo failed. taskId=[%v]"+
			" effectRows=[%v] err=[%v]", agentReportMsg.Taskid, effectRows, err)
		common.Log.Error(errStr)
		return errors.New(errStr)
	}

	//执行成功 更新对应的任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent. msgType=[%v] mdc-server recv exec xtrabackup success report", agentReportMsg.MdcAgentTaskType)
	if err := data.UpdateXtraTaskStatus(agentReportMsg.GetTaskid(), pb_server.XtraTaskStatus_XtraExecSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//对数据发起打包操作
	if err = handleUnpackData(agentReportMsg.GetTaskid(), agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetBaseMessage().GetClusterName()); err != nil {
		errMsg := fmt.Sprintf("handleUnpackData failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 发起打包任务
func handleUnpackData(taskId int64, clusterId int64, nodeId int64, clusterName string) error {
	//获得需要打包的数据
	var (
		recordMap  map[string][]string
		idList     []int64
		targetPath string
	)
	statusList := []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}
	err, recordList := data.GetBinlogRecordList(clusterId, nodeId, statusList)
	if err != nil {
		errMsg := fmt.Sprintf("GetBinlogRecordList failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	msgList := make([]*pb_agent.PackDataBase, 0)
	if len(recordList) == 0 || recordList == nil {
		common.Log.Warn("handleUnpackData,binlogRecordList is nil")
	} else {
		for _, record := range recordList {
			idList = append(idList, record.Id)
			if recordMap == nil {
				recordMap = make(map[string][]string)
			}
			if _, ok := recordMap[record.RemoteIp]; !ok {
				recordMap[record.RemoteIp] = make([]string, 0)
			}
			binlogFileNameList := strings.Split(record.BinlogFileName, ",")
			for _, val := range binlogFileNameList {
				recordMap[record.RemoteIp] = append(recordMap[record.RemoteIp], fmt.Sprintf("%v/%v", record.RemotePath, val))
			}
		}
		for key, val := range recordMap {
			msg := &pb_agent.PackDataBase{
				SourceIp: key,
				DataDir:  val,
			}
			msgList = append(msgList, msg)
		}
	}
	//根据任务id获取xtra备份任务目录
	err, bkInfo := data.GetXtraTaskInfo(clusterId, nodeId, taskId)
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("GetXtraTaskInfo failed, err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 程序异常panic
	index := strings.LastIndex(bkInfo.BkRemotePath, ".")
	targetPath = bkInfo.BkRemotePath[0:index]
	if strings.Contains(bkInfo.BkRemotePath, common.XbstreamType) {
		targetPath = bkInfo.BkRemotePath
	}
	//发送打包的msg
	bkReq := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_UNPACK_DATA,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterName: clusterName,
			NodeId:      nodeId,
			ClusterId:   clusterId,
		},
		TaskId: taskId,
		MsgType: &pb_agent.MdcAgentAsyncMsg_UnpackData{
			UnpackData: &pb_agent.UnpackData{
				DataSource:   msgList,
				TargetPath:   targetPath,
				BinlogTaskId: idList,
			},
		},
	}

	common.Log.Warn("handleUnpackData,DataSource=[%v],DataSource=[%v],", msgList, bkReq.GetUnpackData().DataSource)
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// 备份成功进行数据打包任务默认都是大磁盘处理,使用mdc-agent端口
	agentAddr := fmt.Sprintf("%s:%d", bkInfo.BkRemoteIp, common.Config.AgentPort)
	if _, err := communicator.SendAsyncMsgToDagent(bkReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

// 处理mdc-agent打包数据汇报任务
func handleUnpackDataReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec unpack data fail report,err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv responce from mdc-agent，msgType=[%v],%s", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateXtraTaskStatus(agentReportMsg.Taskid, pb_server.XtraTaskStatus_UnpackFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}

		if agentReportMsg.GetUnpackDataReport() != nil && agentReportMsg.GetUnpackDataReport().BinlogTaskId != nil && len(agentReportMsg.GetUnpackDataReport().BinlogTaskId) > 0 {
			//更新binlog任务状态
			if err := data.UpdateBinlogRecordStatus(agentReportMsg.GetUnpackDataReport().BinlogTaskId, pb_server.BinlogRecordStatus_UnpackWithXtraFail); err != nil {
				errMsg := fmt.Sprintf("UpdateBinlogRecordStatus failed,err=[%v]", err)
				common.Log.Warn(errMsg)
				return errors.New(errMsg)
			}
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应xtra任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv responce from mdc-agent，msgType=[%v],exec unpack success", agentReportMsg.MdcAgentTaskType)
	if err := data.UpdateXtraTaskStatus(agentReportMsg.Taskid, pb_server.XtraTaskStatus_UnpackSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	if agentReportMsg.GetUnpackDataReport().GetBinlogTaskId() != nil && len(agentReportMsg.GetUnpackDataReport().GetBinlogTaskId()) > 0 {
		//更新binlog任务记录状态
		if err := data.UpdateBinlogRecordStatus(agentReportMsg.GetUnpackDataReport().GetBinlogTaskId(), pb_server.BinlogRecordStatus_UnpackWithXtraSuccess); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}

	//根据任务id获取xtra备份任务目录
	err, bkInfo := data.GetXtraTaskInfo(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetTaskid())
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("GetXtraTaskInfo failed,err=[%v],agentReportMsg=[%v]", err, agentReportMsg)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//构建上传dba bos的rpc任务
	bkReq := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_UPLOADDBABOS,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   bkInfo.ClusterId,
			NodeId:      bkInfo.NodeId,
			ClusterName: bkInfo.ClusterName,
		},
		TaskId: bkInfo.TaskId,
		MsgType: &pb_agent.MdcAgentAsyncMsg_UploadDbaBos{
			UploadDbaBos: &pb_agent.UploadDbaBosData{
				DataDir:   bkInfo.BkRemotePath,
				DbaBosDir: bkInfo.BkBosPath,
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// 上传dba bos任务，都是大磁盘机器处理，使用mdc-agent默认端口
	agentAddr := fmt.Sprintf("%s:%d", bkInfo.BkRemoteIp, common.Config.AgentPort)
	if _, err := communicator.SendAsyncMsgToDagent(bkReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, bkReq, agentAddr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 处理mdc-agent的执行上传bos汇报
func handleUploadBosReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec upload dbaBos failed,err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent，msgType=[%v],%s", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateXtraTaskStatus(agentReportMsg.GetTaskid(), pb_server.XtraTaskStatus_UploadDbaBosFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应的任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent，msgType=[%v],mdc-server recv exec upload dbaBos success report", agentReportMsg.MdcAgentTaskType)
	if err := data.UpdateXtraTaskStatus(agentReportMsg.GetTaskid(), pb_server.XtraTaskStatus_UploadDbaBosSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 上传DBA bos成功后获取当前天集群备份任务信息
	err, xtraTask := data.GetXtraTaskInfo(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetTaskid())
	if err != nil {
		errMsg := fmt.Sprintf("GetXtraTaskInfo failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 更新dba bos路径
	if agentReportMsg.GetUploadDBABOSReport().GetBosPathFile() != "" && agentReportMsg.GetUploadDBABOSReport().GetBosBucket() != "" {
		xtraTask.BkBosPath = agentReportMsg.GetUploadDBABOSReport().GetBosPathFile()
		xtraTask.BkBosBucket = agentReportMsg.GetUploadDBABOSReport().GetBosBucket()
		xtraTask.BkBinlogBosPath = agentReportMsg.GetUploadDBABOSReport().GetBosBinlogFile()
		_, err = xtraTask.UpdateByIndexs([]string{"BkBosPath", "BkBosBucket", "BkBinlogBosPath"})
		if err != nil {
			errMsg := fmt.Sprintf("[handleUploadBosReport] update BkBosPath/BkBosBucket failed. err=[%v] taskId=[%v] id=[%v]", err, agentReportMsg.GetTaskid(), xtraTask.Id)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}

	bkInfo := new(daoMdc.BkTaskInfo)
	//更新对应集群备份任务表中Lsnpos
	bkInfo.ClusterId = xtraTask.ClusterId
	bkInfo.NodeId = xtraTask.NodeId
	bkInfo.BkLsnPos = xtraTask.LsnTo
	bkInfo.BkCurrentCycle = xtraTask.BkCycleStatus
	updateCond := []string{"ClusterId", "NodeId"}
	updatCol := []string{"BkLsnPos", "BkCurrentCycle"}
	_, err = bkInfo.UpdateByCondCols(updateCond, updatCol)
	if err != nil {
		errMsg := fmt.Sprintf("update bkInfo failed , error=[%v], clusetrId=[%v],nodeId=[%v]", err,
			bkInfo.ClusterId, bkInfo.NodeId)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}
	//判断该集群是否向siod灾备平台注册过
	err, bkInfo = data.GetBkTaskInfo(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId())
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("GetBkTaskInfo failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	var protocolId int64
	if bkInfo.ProtocolId == 0 {
		//如果没有注册过 先发起注册到siod bos操作
		if err, protocolId = registerSiodBos(bkInfo.ClusterName, bkInfo.ClusterId, bkInfo.NodeId, agentReportMsg.GetTaskid()); err != nil || protocolId < 0 {
			errMsg := fmt.Sprintf("registerSiodBos failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	} else {
		protocolId = bkInfo.ProtocolId
	}
	//发起上传数据到siod bos操作
	if err = uploadSiodBos(protocolId, bkInfo.ClusterName, bkInfo.ClusterId, bkInfo.NodeId, agentReportMsg.GetTaskid()); err != nil {
		errMsg := fmt.Sprintf("uploadSiodBos failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

// 注册到siod bos
func registerSiodBos(clusterName string, clusterId int64, nodeId int64, taskId int64) (error, int64) {
	var (
		err            error
		siodRegister   common.SiodRegister
		resp           common.SiodPlatResult
		marshalByte    []byte
		protocolId     int64
		sourceDataPath string
	)
	if nodeId == 0 {
		sourceDataPath = fmt.Sprintf("bos:/%v/%v_%v", common.Config.DbaBosBucket, clusterName, clusterId)
	} else {
		sourceDataPath = fmt.Sprintf("bos:/%v/%v_%v_%v", common.Config.DbaBosBucket, clusterName, clusterId, nodeId)
	}
	//构造备份发起请求
	siodRegister = common.SiodRegister{
		Platform: common.Config.SiodBackupPlatform,
		Token:    common.Config.SiodRegisterToken,
		Conf: common.SiodRegisterConf{
			CreateType:        common.SiodRegisterCreateType,
			Product:           common.SiodRegisterProduct,
			Subsystem:         common.SiodRegistersubSystem,
			DataType:          common.SiodRegisterDataType,
			SourceDataType:    common.SiodRegisterSourceDataType,
			BackupInstanceNum: common.SiodRegisterBackupInstanceNum,
			SourceDataPath:    sourceDataPath,
			BackupUser:        common.SiodRegisterBackupUser,
			RunningType:       common.SiodRegisterRunningType,
			OpOwner:           common.SiodRegisterOpOwner,
			RdOwner:           common.SiodRegisterRdOwner,
			NeedTape:          common.SiodRegisterNeedType,
			RetentionTime:     common.SiodRegisterRetentionTime,
		},
	}
	//判断是单分片集群
	//根据集群名查询备份任务信息表
	bkInfo := new(daoMdc.BkTaskInfo)
	bkInfo.ClusterName = clusterName
	bkInfoList, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterName"})
	if err != nil || bkInfoList == nil {
		errMsg := fmt.Sprintf("ReadBackUpInfoByCols failed, error=[%v]", err)
		common.Log.Warning(errMsg)
		return err, -1
	}
	if len(bkInfoList) == 1 {
		//生成单分片集群的APPName
		siodRegister.Conf.AppName = fmt.Sprintf("%v-%v", common.AppPrefix, clusterName)
	} else {
		//生成多分片集群的APPName
		err, bkInfo := data.GetBkTaskInfo(clusterId, nodeId)
		if err != nil {
			errMsg := fmt.Sprintf("GetBkTaskInfo failed, error=[%v]", err)
			common.Log.Warning(errMsg)
			return err, -1
		}
		siodRegister.Conf.AppName = fmt.Sprintf("%v-%v-%v", common.AppPrefix, clusterName, bkInfo.NodeName)
	}

	if marshalByte, err = json.Marshal(siodRegister); err != nil {
		errMsg := fmt.Sprintf("marshal siodBkRequest failed, error=[%v]", err)
		common.Log.Warning(errMsg)
		return err, -1
	}
	//1.发送注册请求
	if resp, err = common.HTTPPost(common.Config.SiodBackupAutoRegisterProtocol, bytes.NewReader(marshalByte)); err != nil || resp.Errno != 0 {
		errMsg := fmt.Sprintf("post register request failed, error=[%v], resp=[%+v], cluster_name=[%v],siodRegister=[%v],marshalByte=[%v]", err, resp, clusterName, siodRegister, string(marshalByte))
		common.Log.Warning(errMsg)
		logStr := fmt.Sprintf("mdc-server register to siod_backup failed,%v", errMsg)
		if err := data.UpdateXtraTaskStatus(taskId, pb_server.XtraTaskStatus_RegisterSiodBosFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
		}
		return errors.New(errMsg), -1
	} else {
		err, protocolId = common.GetRespDataVal(resp.Data)
		if err != nil {
			errMsg := fmt.Sprintf("getRespDataVal fail, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg), -1
		}
		//成功 回填protocolId至数据库,
		common.Log.Notice("UpdateBkInfoProtocolId cluster_id=[%v],node_id=[%v],protocolId=[%v]", clusterId, nodeId, protocolId)
		if err = data.UpdateBkInfoProtocolId(clusterId, nodeId, protocolId); err != nil {
			errMsg := fmt.Sprintf("UpdateBkInfoProtocolId fail, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg), -1
		}
		//更新xtra task任务状态
		logStr := fmt.Sprintf("mdc-server register to siod_backup successful")
		if err = data.UpdateXtraTaskStatus(taskId, pb_server.XtraTaskStatus_RegisterSiodBosSuccess, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg), -1
		}
	}
	return nil, protocolId
}

// 上传siod bos
func uploadSiodBos(protocolId int64, clusterName string, clusterId int64, nodeId int64, taskId int64) error {
	var (
		err           error
		siodBkRequest common.SiodBkRequest
		resp          common.SiodPlatResult
		marshalByte   []byte
	)
	err, xtraTask := data.GetXtraTaskInfo(clusterId, nodeId, taskId)
	if err != nil || xtraTask == nil {
		errMsg := fmt.Sprintf("GetXtraTaskInfo failed, error=[%v]", err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}

	dataVersion := common.GetDataVersion(xtraTask.BkBosPath)
	if dataVersion == "" {
		errMsg := fmt.Sprintf("GetXtraTask data version failed, dataVersion=[%v] error=[%v]", dataVersion, err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}

	// 获取当前集群或分片dbbk-name
	bkInfo := new(daoMdc.BkTaskInfo)
	bkInfo.ClusterId = clusterId
	bkInfo.NodeId = nodeId
	bkInfoList, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if err != nil || bkInfoList == nil || len(bkInfoList) != 1 {
		errMsg := fmt.Sprintf("ReadBackUpInfoByCols failed,bkInfo=[%v] error=[%v]", bkInfoList, err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}

	//构造备份发起请求
	siodBkRequest = common.SiodBkRequest{
		Platform:    common.Config.SiodBackupPlatform,
		Token:       common.Config.SiodRegisterToken,
		ProtocolID:  protocolId,
		ProductName: common.SiodBackupProductName,
		DataVersion: dataVersion,
		AppName:     bkInfoList[0].BkBns,
	}
	if marshalByte, err = json.Marshal(siodBkRequest); err != nil {
		errMsg := fmt.Sprintf("marshal siodBkRequest failed, error=[%v]", err)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)
	}
	//发送发起备份请求
	if resp, err = common.HTTPPost(common.Config.SiodBackupManualRun, bytes.NewReader(marshalByte)); err != nil || resp.Errno != 0 {
		errMsg := fmt.Sprintf("post backup request failed, error=[%v], resp=[%+v], cluster_name=[%v],siodBkRequest=[%v]", err, resp, clusterName, siodBkRequest)
		common.Log.Warning(errMsg)
		logStr := fmt.Sprintf("mdc-server start siod backup requset failed,%v", errMsg)
		if err := data.UpdateXtraTaskStatus(taskId, pb_server.XtraTaskStatus_UploadSiodBosFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg)
		}
	} else {
		//成功 更新任务状态
		logStr := fmt.Sprintf("mdc-server start siod backup requset successful")
		if err = data.UpdateXtraTaskStatus(taskId, pb_server.XtraTaskStatus_UploadSiodBosReicer, logStr); err != nil {
			errMsg := fmt.Sprintf("UpdateXtraTaskStatus fail, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg)
		}
		err, taskInfoId := common.GetRespDataVal(resp.Data)
		if err != nil {
			errMsg := fmt.Sprintf("getRespDataVal fail, cluster_name=[%v], taskId=[%+v], err=[%v]", clusterName, taskId, err)
			common.Log.Warning(errMsg)
			return errors.New(errMsg)
		}
		//更新taskInfoId
		xtraRecordTask := new(daoMdc.BkXtraTaskExecRecord)
		xtraRecordTask.TaskId = taskId
		xtraRecordTask.SiodTaskInfoId = taskInfoId
		//需要更新的列
		conds := []string{"TaskId"}
		cols := []string{"SiodTaskInfoId"}
		_, err = xtraRecordTask.UpdateByCondCols(conds, cols)
		if err != nil {
			common.Log.Warn("failed to update xtraTask info. clusterId=[%v] err=[%v]", clusterId, err)
			return err
		}

	}
	return nil
}

// 处理mdc-agent下载数据汇报;进行恢复操作
func handleDownloadDataReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec download data failed,err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent,msg=[%v],%v", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_DownLoadDataFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应的任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent,msg=[%v],exec download data sucessful", agentReportMsg.MdcAgentTaskType)
	if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_DownLoadDataSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得恢复记录
	err, rsTask := data.GetRsTaskInfo(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetTaskid())
	if err != nil || rsTask == nil {
		errMsg := fmt.Sprintf("GetRsTaskInfo failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	var (
		rsReq              pb_agent.MdcAgentAsyncMsg
		expandMdcAgentPort int64
	)

	switch agentReportMsg.MdcAgentTaskType {
	case pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA:
		expandMdcAgentPort = agentReportMsg.GetDownLoadRsDataReport().GetMdcAgentPort()
		//构建恢复数据的请求
		rsReq = pb_agent.MdcAgentAsyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_RESTORE,
			TaskId:           rsTask.Id,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   rsTask.ClusterId,
				NodeId:      rsTask.NodeId,
				ClusterName: rsTask.ClusterName,
			},
			MsgType: &pb_agent.MdcAgentAsyncMsg_ExecRestore{
				ExecRestore: &pb_agent.ExecRestore{
					RestoreType:            rsTask.RestoreType,
					BaseDataPath:           agentReportMsg.GetDownLoadRsDataReport().BaseDataPath,
					IncDataPath:            agentReportMsg.GetDownLoadRsDataReport().IncDataPath,
					BinlogPath:             agentReportMsg.GetDownLoadRsDataReport().BinlogPath,
					RsTime:                 rsTask.RestoreTime,
					RsTableName:            rsTask.OperateTableName,
					WhiteIPList:            rsTask.TempInsIpList,
					AccountUser:            rsTask.ApplicantName,
					InstanceIp:             agentReportMsg.ClientIp,
					TargetPath:             agentReportMsg.GetDownLoadRsDataReport().TargetPath,
					ApplicantRole:          int32(rsTask.ApplicantRole),
					RestorePartitionConfig: rsTask.RestoreConfig,
				},
			},
		}
	case pb_server.MdcAgentTaskType_DOWNLOAD_BINLOG_DATA:
		expandMdcAgentPort = agentReportMsg.GetDownLoadRsBinlogDataReport().GetMdcAgentPort()
		//获得备库IP和端口
		backupInstance, err := data.GetClusterInstance(agentReportMsg.GetBaseMessage().ClusterId, agentReportMsg.GetBaseMessage().NodeId, pb_tinker.InstanceRole_BACKUP, agentReportMsg.GetBaseMessage().GetIsFdb())
		if err != nil || backupInstance == nil {
			errMsg := fmt.Sprintf("Failed to GetClusterInstance from db. agentReportMsg=[%v] err=[%v]",
				agentReportMsg, err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
		//获得备库目录
		err, bkInfo := data.GetBkTaskInfo(agentReportMsg.GetBaseMessage().ClusterId, agentReportMsg.GetBaseMessage().NodeId)
		if err != nil || bkInfo == nil {
			errMsg := fmt.Sprintf("Failed to GetBkTaskInfo from db. agentReportMsg=[%v] err=[%v]",
				agentReportMsg, err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
		//构建解析binlog的请求
		rsReq = pb_agent.MdcAgentAsyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG,
			TaskId:           rsTask.Id,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   rsTask.ClusterId,
				NodeId:      rsTask.NodeId,
				ClusterName: rsTask.ClusterName,
			},
			MsgType: &pb_agent.MdcAgentAsyncMsg_ExecParseBinlog{
				ExecParseBinlog: &pb_agent.ExecParseBinlog{
					BinlogPath: agentReportMsg.GetDownLoadRsBinlogDataReport().BinlogPath,
					StartTime:  rsTask.RollbackBeginTime,
					Endtime:    rsTask.RollbackEndTime,
					BkIp:       backupInstance.Ip,
					BkPort:     int64(backupInstance.Port),
					BkPath:     bkInfo.BkMysqlBasedir,
				},
			},
		}
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	var agentAddr string
	if expandMdcAgentPort != 0 {
		agentAddr = fmt.Sprintf("%s:%d", agentReportMsg.ClientIp, expandMdcAgentPort)
	} else {
		// 未指定端口此请求走向DBA默认恢复机器
		agentAddr = fmt.Sprintf("%s:%d", agentReportMsg.ClientIp, common.Config.AgentPort)
	}

	if _, err := communicator.SendAsyncMsgToDagent(rsReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, rsReq, agentAddr)
		common.Log.Warn(errMsg)
		logStr = fmt.Sprintf("mdc-server send msg to mdc-agent,msg=[%v],%v", agentReportMsg.MdcAgentTaskType, errMsg)
		if err = data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应的任务的执行状态
	logStr = fmt.Sprintf("mdc-server send msg to mdc-agent. msg=[%v] info=[%+v] send async task to mdc-agent successful",
		agentReportMsg.MdcAgentTaskType, rsReq.GetMsgType())
	if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoring, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 处理下载binlog数据汇报任务
func handleDownloadBinlogDataReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec download data failed,err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent,msg=[%v],%v", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_DownLoadDataFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应的任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent,msg=[%v],exec download data sucessful,clusetrId=[%v],nodeId=[%v],agentIp=[%v]", agentReportMsg.MdcAgentTaskType, agentReportMsg.BaseMessage.ClusterId, agentReportMsg.BaseMessage.NodeId, agentReportMsg.ClientIp)
	if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_DownLoadDataSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得恢复记录
	err, rsTask := data.GetRsTaskInfo(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetTaskid())
	if err != nil || rsTask == nil {
		errMsg := fmt.Sprintf("GetRsTaskInfo failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	var rsReq pb_agent.MdcAgentAsyncMsg

	//获得备库IP和端口
	backupInstance, err := data.GetClusterInstance(agentReportMsg.GetBaseMessage().ClusterId, agentReportMsg.GetBaseMessage().NodeId, pb_tinker.InstanceRole_BACKUP, agentReportMsg.GetBaseMessage().GetIsFdb())
	if err != nil || backupInstance == nil {
		errMsg := fmt.Sprintf("Failed to GetClusterInstance from db. agentReportMsg=[%v] err=[%v]",
			agentReportMsg, err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得备库目录
	err, bkInfo := data.GetBkTaskInfo(agentReportMsg.GetBaseMessage().ClusterId, agentReportMsg.GetBaseMessage().NodeId)
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("Failed to GetBkTaskInfo from db. agentReportMsg=[%v] err=[%v]",
			agentReportMsg, err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//向备库的agent发送获得账户密码的同步请求
	getInfoReq := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_GET_BKMYSQL_INFO,
		MsgType: &pb_agent.MdcAgentSyncMsg_GetBkMysqlInfo{
			GetBkMysqlInfo: &pb_agent.GetBkMysqlInfo{
				BkMysqlPath: bkInfo.BkMysqlBasedir,
				RemoteIp:    agentReportMsg.ClientIp,
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	agentAddr := fmt.Sprintf("%v:%v", backupInstance.Ip, backupInstance.MdcAgentPort)
	res, err := communicator.SendSyncMsgToDagent(getInfoReq, agentAddr)
	if err != nil {
		errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, rsReq, agentAddr)
		common.Log.Warn(errMsg)
		logStr = fmt.Sprintf("mdc-server send msg to mdc-agent,msg=[%v],%v", agentReportMsg.MdcAgentTaskType, errMsg)
		if err = data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}

	//构建解析binlog的请求
	rsReq = pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG,
		TaskId:           rsTask.Id,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterId:   rsTask.ClusterId,
			NodeId:      rsTask.NodeId,
			ClusterName: rsTask.ClusterName,
		},
		MsgType: &pb_agent.MdcAgentAsyncMsg_ExecParseBinlog{
			ExecParseBinlog: &pb_agent.ExecParseBinlog{
				BinlogPath: agentReportMsg.GetDownLoadRsBinlogDataReport().BinlogPath,
				StartTime:  rsTask.RollbackBeginTime,
				Endtime:    rsTask.RollbackEndTime,
				BkIp:       backupInstance.Ip,
				BkPort:     int64(backupInstance.Port),
				BkPath:     bkInfo.BkMysqlBasedir,
				User:       res.GetGetBkMysqlInfoRes().BkUser,
				Password:   res.GetGetBkMysqlInfoRes().BkPassword,
			},
		},
	}

	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	if agentReportMsg.GetDownLoadRsBinlogDataReport().GetMdcAgentPort() != 0 {
		agentAddr = fmt.Sprintf("%s:%d", agentReportMsg.ClientIp, agentReportMsg.GetDownLoadRsBinlogDataReport().GetMdcAgentPort())
	} else {
		// 未指定端口默认走向DBA默认可用恢复机器
		agentAddr = fmt.Sprintf("%s:%d", agentReportMsg.ClientIp, common.Config.AgentPort)
	}
	if _, err := communicator.SendAsyncMsgToDagent(rsReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, rsReq, agentAddr)
		common.Log.Warn(errMsg)
		logStr = fmt.Sprintf("mdc-server send msg to mdc-agent,msg=[%v],%v", agentReportMsg.MdcAgentTaskType, errMsg)
		if err = data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//执行成功 更新对应的任务的执行状态
	logStr = fmt.Sprintf("mdc-server send msg to mdc-agent,msg=[%v],send async task to mdc-agent successful", agentReportMsg.MdcAgentTaskType)
	if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoring, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 处理恢复汇报任务
func handleExecRestoreReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("exec restore failed. err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务的执行状态
		logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent msg=[%v] errMsg=[%v]", agentReportMsg.MdcAgentTaskType, errMsg)
		if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreFail, logStr); err != nil {
			errMsg = fmt.Sprintf("UpdateRsTaskStatus failed. err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}
	//找到任务记录
	err, rsInfo := data.GetRsTaskInfo(agentReportMsg.GetBaseMessage().ClusterId, agentReportMsg.GetBaseMessage().GetNodeId(), agentReportMsg.GetTaskid())
	if err != nil || rsInfo == nil {
		errMsg := fmt.Sprintf("GetRsTaskInfo failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 根据任务类型不同，进入其他的处理分支
	switch global.RsType(rsInfo.RestoreType) {
	case global.RestorePartition:
		common.Log.Info("receive RestorePartition restore type. PartitionBosMapping=[%+v]", agentReportMsg.GetExecRestoreReport().PartitionBosMapping)
		if err = handleRestorePartition(agentReportMsg); err != nil {
			//执行失败 更新对应的任务的执行状态
			logStr := fmt.Sprintf("handleRestorePartition failed. msg=[%+v] errMsg=[%v]", agentReportMsg, err)
			if err = data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreFail, logStr); err != nil {
				errMsg := fmt.Sprintf("UpdateRsTaskStatus failed. err=[%v]", err)
				common.Log.Warn(errMsg)
				return err
			}
			return err
		}
		return nil
	case global.RestoreInstance:
		// 写入日志
		logStr := fmt.Sprintf("receive RestoreInstance type. TaskId=[%v] DbUser=[%v] DbPassWord=[%v] InstanceIp=[%v] InstancePort=[%v]",
			agentReportMsg.GetTaskid(), agentReportMsg.GetExecRestoreReport().DbUser, agentReportMsg.GetExecRestoreReport().DbPassWord,
			agentReportMsg.GetExecRestoreReport().InstanceIp, agentReportMsg.GetExecRestoreReport().InstancePort)
		common.Log.Info(logStr)
		if err = data.UpdateRsTaskExecLog(agentReportMsg.GetTaskid(), logStr); err != nil {
			common.Log.Error("fail to write log. logId=[%v] err=[%v] comment=[%v] ", agentReportMsg.GetTaskid(),
				err, logStr)
		}

	}
	//执行成功 更新对应的任务的执行状态
	logStr := fmt.Sprintf("mdc-server recv msg from mdc-agent msg=[%v]. exec restore successful clusterId=[%v] nodeId=[%v] agentIp=[%v]", agentReportMsg.MdcAgentTaskType, agentReportMsg.BaseMessage.ClusterId, agentReportMsg.BaseMessage.NodeId, agentReportMsg.ClientIp)
	if err := data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_ExecRestoreSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//判断恢复发起者 如果是dba发起 不执行邮件发送等步骤
	if rsInfo.ApplicantRole == int(common.Dba) {
		common.Log.Notice("mdc-server restore data end,taskId=[%v]", rsInfo.Id)
		return nil
	}
	var accountInfo AccountEmailInfo
	switch agentReportMsg.MdcAgentTaskType {
	case pb_server.MdcAgentTaskType_EXEC_RESTORE:
		//构建邮件内容
		accountInfo = AccountEmailInfo{
			DbUser:      agentReportMsg.GetExecRestoreReport().DbUser,
			DbPassword:  agentReportMsg.GetExecRestoreReport().DbPassWord,
			Address:     fmt.Sprintf("%s:%s", agentReportMsg.GetExecRestoreReport().InstanceIp, agentReportMsg.GetExecRestoreReport().InstancePort),
			TaskId:      agentReportMsg.Taskid,
			ClusterName: agentReportMsg.GetBaseMessage().ClusterName,
		}
	case pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG:
		//构建邮件内容
		accountInfo = AccountEmailInfo{
			Address: fmt.Sprintf("http://sc.duxiaoman-int.com/xweb-plus/api/v1/mdcapi/downLoadSqlResult?taskId=%v", agentReportMsg.GetTaskid()),
			TaskId:  agentReportMsg.Taskid,
		}
		//更新任务记录中binlogSqlPath
		if err = data.UpdateRsTaskBinlogPath(agentReportMsg.GetTaskid(), agentReportMsg.GetExecParseBinlogReport().BosPath); err != nil {
			errMsg := fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	default:
		errMsg := fmt.Sprintf("unknown report Msg,msg=[%v]", agentReportMsg)
		common.Log.Warning(errMsg)
		return errors.New(errMsg)

	}
	//调用发送邮件的接口发送给用户
	sendAccountInfoByEmailToUser(agentReportMsg.MdcAgentTaskType, accountInfo, []string{rsInfo.ApplicantName})
	//更新对应的任务的执行状态
	logStr = fmt.Sprintf("mdc-server send email to user successful")
	if err = data.UpdateRsTaskStatus(agentReportMsg.GetTaskid(), pb_server.RsTaskStatus_SendEmailSuccess, logStr); err != nil {
		errMsg := fmt.Sprintf("UpdateRsTaskStatus failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 发送邮件
func sendAccountInfoByEmailToUser(taskType pb_server.MdcAgentTaskType, accountInfo AccountEmailInfo, userList []string) {
	//邮件内容
	var message, subject string
	switch taskType {
	case pb_server.MdcAgentTaskType_EXEC_RESTORE:
		//邮件主题
		subject = "[DBA通知]数据库数据恢复"
		if accountInfo.Address != "" {
			message = fmt.Sprintf("数据恢复成功！账号信息如下,请妥善保管并注意账号、密码安全,访问方式请联系所属业务的DBA同学<br>"+
				"集群名:%v<br>任务id:%v<br>访问地址:%v<br>用户名:%v<br>密码:%v", accountInfo.ClusterName, accountInfo.TaskId, accountInfo.Address,
				accountInfo.DbUser, accountInfo.DbPassword)
		} else {
			message = fmt.Sprintf("数据恢复成功！账号信息如下,请妥善保管并注意账号、密码安全,访问方式请联系所属业务的DBA同学<br>"+
				"集群名:%v<br>任务id:%v<br>访问地址:%v<br>用户名:%v<br> 密码:%v", accountInfo.ClusterName, accountInfo.TaskId, "请联系DBA索要地址",
				accountInfo.DbUser, accountInfo.DbPassword)
		}
	case pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG:
		subject = "[DBA通知]数据库binlog解析回滚"
		if accountInfo.Address != "" {
			message = fmt.Sprintf("数据解析回滚成功！连接信息如下，请点击连接进行下载。<br>"+
				"集群名:%v<br>任务id:%v<br>访问地址:<a href='%v'>点击此处下载sql文件</a><br>", accountInfo.ClusterName, accountInfo.TaskId, accountInfo.Address)
		} else {
			message = fmt.Sprintf("数据解析回滚成功！连接信息如下，请点击连接进行下载。<br>"+
				"集群名:%v<br>任务id:%v<br>访问地址:<a href='%v'>点击此处下载sql文件</a><br>", accountInfo.ClusterName, accountInfo.TaskId, "请联系DBA索要地址")
		}
	}
	//发送邮件
	common.Sendmail(subject, message, userList, nil, nil)
	common.Log.Notice("Successful to send AccountInfoEmail to user ,clusterName=[%v],userList=[%v]", accountInfo.ClusterName, userList)
	return
}

// 处理探测binlog备份任务结果
func handleCheckBinlogReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	//判断是否执行成功
	if agentReportMsg.GetErrorMsg() != "" {
		errMsg := fmt.Sprintf("check binlog status fail report,err=[%v]", agentReportMsg.GetErrorMsg())
		common.Log.Warn(errMsg)
		//执行失败 更新对应的任务binlog检查状态为失败
		if err := data.UpdateCheckBinlogStatus(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), pb_server.BinlogCheckStatus_CheckFail); err != nil {
			errMsg = fmt.Sprintf("UpdateXtraTaskStatus failed,err=[%v]", err)
			common.Log.Warn(errMsg)
		}
		return errors.New(errMsg)
	}

	//获取探测对应集群任务信息
	bkInfo := new(daoMdc.BkTaskInfo)
	bkInfo.ClusterId = agentReportMsg.GetBaseMessage().GetClusterId()
	bkInfo.NodeId = agentReportMsg.GetBaseMessage().GetNodeId()
	infoSlice, err := bkInfo.ReadBackUpInfoByCols([]string{"ClusterId", "NodeId"})
	if nil != err || len(infoSlice) != 1 {
		common.Log.Warn("Failed to get bk_task info from db. ClusterId=[%v] nodeId=[%v] err=[%v]",
			agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), err)
		return err
	}

	//如果binlog进程号发生改变;同时 binlog进程号不等于0修改BinlogServerId;
	if agentReportMsg.GetCheckBinlogStatusReport().GetBinlogServerId() != infoSlice[0].BinlogServerId &&
		agentReportMsg.GetCheckBinlogStatusReport().GetBinlogServerId() != 0 {
		infoSlice[0].BinlogServerId = agentReportMsg.GetCheckBinlogStatusReport().GetBinlogServerId()
		conds := []string{"ClusterId", "NodeId"}
		cols := []string{"BinlogServerId"}
		effectRows, err := infoSlice[0].UpdateByCondCols(conds, cols)
		if nil != err || effectRows != 1 {
			errStr := fmt.Sprintf("Fail to dealBinlogDetect due to update bkTaskInfo failed. clusterId=[%v]"+
				" effectRows=[%v] err=[%v]", agentReportMsg.GetBaseMessage().GetClusterId(), effectRows, err)
			common.Log.Error(errStr)
			return errors.New(errStr)
		}
	}

	// 将binlog备份任务状态置为初始状态
	err = data.UpdateCheckBinlogStatus(agentReportMsg.GetBaseMessage().GetClusterId(), agentReportMsg.GetBaseMessage().GetNodeId(), pb_server.BinlogCheckStatus_CheckInit)
	if err != nil {
		errMsg := fmt.Sprintf("check binlog backup task success but update binlog status failed, err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	// 如果本次探测无新binlog文件产生 直接返回
	if agentReportMsg.GetCheckBinlogStatusReport().GetBinlogFileNameResult() == "" {
		return nil
	}
	//如果本次探测有新binlog产生 执行以下流程
	//将备份完成的binlog记录到表中
	// 查询实例信息表获得备库库实例机器IP
	backupInstance, err := data.GetClusterInstance(infoSlice[0].ClusterId, infoSlice[0].NodeId, pb_tinker.InstanceRole_BACKUP, agentReportMsg.GetBaseMessage().GetIsFdb())
	if err != nil {
		common.Log.Warn("Failed to get back instance info from db. ClusterId=[%v] err=[%v]",
			agentReportMsg.GetBaseMessage().GetClusterId(), err)
		return err
	}

	_, err = data.AddBinlogRecord(backupInstance.Ip, agentReportMsg.BaseMessage, agentReportMsg.GetCheckBinlogStatusReport())
	if err != nil {
		errMsg := fmt.Sprintf("AddBinlogRecord failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//删除备库实例上的备份的binlog
	bkReq := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DELETE_BINLOGBK,
		MsgType: &pb_agent.MdcAgentSyncMsg_DeleteBinlogBk{
			DeleteBinlogBk: &pb_agent.DeleteBinlogBk{
				BkBinlogDir:          agentReportMsg.GetCheckBinlogStatusReport().BkBinlogPath,
				BinlogFileNameResult: agentReportMsg.GetCheckBinlogStatusReport().BinlogFileNameResult,
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	agentAddr := fmt.Sprintf("%s:%d", backupInstance.Ip, backupInstance.MdcAgentPort)
	_, err = communicator.SendSyncMsgToDagent(bkReq, agentAddr)
	if err != nil {
		errMsg := fmt.Sprintf("SendSyncMsgToDagent failed,err=[%v],agentAddr=[%v],bkReq=[%v]", err, agentAddr, bkReq)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 处理分盘逻辑
func handleRestorePartition(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	// 1.将已上传至bos的路径回填至数据库
	rpcRestoreConfig := agentReportMsg.GetExecRestoreReport().PartitionBosMapping
	rsTask := daoMdc.RSTaskRecord{
		Id:            agentReportMsg.GetTaskid(),
		RestoreConfig: rpcRestoreConfig,
	}
	count, err := rsTask.UpdateByIndexs([]string{"RestoreConfig"})
	if err != nil || count != 1 {
		return fmt.Errorf("update RestoreConfig failed. rsTask=[%+v]", rsTask)
	}
	err = rsTask.ReadById()
	if err != nil {
		common.Log.Error("can't get rsTask from db. rsTask=[%v]", rsTask.Id)
		return err
	}

	// 2.在数据库产生对应的子任务，并向其agent发起恢复任务
	rsPartitionConfig := new(global.RestorePartitionConfig)
	// 解析分区配置
	err = json.Unmarshal([]byte(rpcRestoreConfig), rsPartitionConfig)
	if err != nil {
		common.Log.Error("can't Unmarshal RestoreConfig=[%v]", rsTask.RestoreConfig)
		return err
	}
	// 校验bos上传分区不为空
	// 主分区bos路径不为空
	if rsPartitionConfig.VarBosUploadPath == "" || rsTask.TargetMachinePath == "" {
		return fmt.Errorf("bos upload path can't be empty. VarBosUploadPath=[%v] TargetMachinePath=[%v]", rsPartitionConfig.VarBosUploadPath, rsTask.TargetMachinePath)
	}
	// 其他分区bos路径不为空
	for _, item := range rsPartitionConfig.DBMapping {
		if item.BosUploadPath == "" {
			return fmt.Errorf("bos upload path can't be empty. bosUploadPath=[%v]", item.BosUploadPath)
		}
	}
	// 生成子任务
	for _, agentIp := range rsPartitionConfig.IP {
		rsSubTask := daoMdc.RSSubTaskRecord{
			TaskType:   int32(global.RsSubPartition),
			RestoreIp:  agentIp,
			FTaskId:    rsTask.Id,
			CreateTime: time.Now().Format(common.TimeFormat),
			UpdateTime: time.Now().Format(common.TimeFormat),
		}
		subTaskId, err := rsSubTask.InsertOneRecord()
		if err != nil {
			common.Log.Error("can't insert rsSubTask err=[%v]", err)
			return err
		}
		//发送任务给各个agent
		bkReq := pb_agent.MdcAgentAsyncMsg{
			MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_PARTITION_RESTORE,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterId:   rsTask.ClusterId,
				NodeId:      rsTask.NodeId,
				ClusterName: rsTask.ClusterName,
			},
			TaskId: subTaskId,
			MsgType: &pb_agent.MdcAgentAsyncMsg_RestorePartitionData{RestorePartitionData: &pb_agent.RestorePartitionData{
				RestorePartitionConfig: rsTask.RestoreConfig,
				// 兼容原有恢复目录逻辑， 恢复完成后会在此目录下产生var
				RestoreDir: fmt.Sprintf("%v/sourceData/base", rsTask.TargetMachinePath),
			}},
		}
		agentAddr := fmt.Sprintf("%s:%d", agentIp, rsPartitionConfig.RsMdcAgentPort)
		if _, err := communicator.SendAsyncMsgToDagent(bkReq, agentAddr); err != nil {
			errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v] req=[%v] agentAddr=[%v]", err, bkReq, agentAddr)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}
	return nil
}

// 处理分盘完成后的汇报消息
func handleRestorePartitionReport(agentReportMsg *pb_server.MdcAgentReportMsg) error {
	// 1.更新子任务类型为恢复完成
	rss := daoMdc.RSSubTaskRecord{
		Id: agentReportMsg.Taskid,
	}
	if agentReportMsg.ErrorMsg != "" {
		rss.TaskStatus = int32(global.RsSubTaskFailed)
	} else {
		rss.TaskStatus = int32(global.RsSubTaskSuccess)
	}
	// 更新任务状态
	_, err := rss.UpdateByIndexs([]string{"TaskStatus"})
	if err != nil {
		return err
	}
	// 查询任务父id
	if err = rss.ReadById(); err != nil {
		return err
	}
	// 父任务记录
	rsTask := new(daoMdc.RSTaskRecord)
	rsTask.Id = rss.FTaskId
	// 2.判断是否所有子任务恢复完成，若恢复完成，将主任务修改为恢复完成
	rsSubTasks, err := rss.ReadRsInfoByCols([]string{"FTaskId"})
	if err != nil {
		return err
	}
	// 判断子任务是否全部运行完成，若其中一个不为运行成功则return
	for _, task := range rsSubTasks {
		if task.TaskStatus != int32(global.RsSubTaskSuccess) {
			// 如果其中一个恢复失败, 父任务需要置为失败
			if task.TaskStatus == int32(global.RsSubTaskFailed) {
				comment := fmt.Sprintf("%v sub task restore failed. rsSubTask=[%+v] err=[%v]",
					time.Now().Format(common.TIME_FORMAT), rsSubTasks, agentReportMsg.ErrorMsg)
				return data.UpdateRsTaskStatus(rsTask.Id, pb_server.RsTaskStatus_ExecRestoreFail, comment)
			}
			return nil
		}
	}
	// 进入到此逻辑，说明子任务已经全部运行完成，更新主任务状态为运行成功
	comment := fmt.Sprintf("%v all sub task restore success. rsSubTask=[%+v]", time.Now().Format(common.TIME_FORMAT), rsSubTasks)
	return data.UpdateRsTaskStatus(rsTask.Id, pb_server.RsTaskStatus_ExecRestoreSuccess, comment)
}
