package worker

import (
	"dt-common/global"
	"errors"
	"fmt"
	"strings"
	"time"

	dao_mdc "dt-common/dao/mdc"
	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
)

type restoreDataPath struct {
	BasePath    string
	IncDataPath []string
	BinlogPath  []string
	BinlogType  pb_agent.SourceType
}

//处理恢复任务请求
func dealRestoreData(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	var (
		remoteIp, workDir, targetPath string
		agentAddr                     string
	)
	clusterId := msg.GetBaseMessage().GetClusterId()
	nodeId := msg.GetBaseMessage().GetNodeId()
	clusterName := msg.GetBaseMessage().GetClusterName()
	rsTime := msg.GetRestoreDataTaskReqMsg().GetRsTime()
	rsType := msg.GetRestoreDataTaskReqMsg().GetRestoreType()
	taskId := msg.GetRestoreDataTaskReqMsg().TaskId
	rsTimeType := msg.GetRestoreDataTaskReqMsg().RsTimeType
	//获得集群备份类型
	err, bkInfo := data.GetBkTaskInfo(clusterId, nodeId)
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("Get backup task info failed, error=[%v],ClusterId=[%v], NodeId=[%v]", err, clusterId, nodeId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	if rsTimeType == int32(common.Snapshot) {
		rsTime = fmt.Sprintf("%v 23:59:59", rsTime)
	}

	//根据实际时间计算拉取数据源类型
	// TODO: 去除根据时间计算获取备份类型的逻辑，改为mdc-agent端识别下载地址，进行数据下载。
	sourceType, err := getSnapshotSourceType(rsTime)
	if err != nil {
		errMsg := fmt.Sprintf("calDownloadSourceType failed, error=[%v],ClusterId=[%v], ClusterName=[%v],NodeId=[%v]", err, bkInfo.ClusterId, bkInfo.ClusterName, bkInfo.NodeId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得用来恢复的数据目录
	dataPath, err := getSourceDataPath(clusterId, nodeId, rsTime, sourceType, rsTimeType, bkInfo.BkType)
	if err != nil || dataPath == nil || (dataPath.BasePath == "" && len(dataPath.BinlogPath) == 0 && len(dataPath.BinlogPath) == 0) {
		errMsg := fmt.Sprintf("getSourceDataPath failed,err=[%v],clusterId=[%v], nodeId=[%v], rsTime=[%v], sourceType=[%v]",
			err, clusterId, nodeId, rsTime, sourceType)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 如果恢复机器或恢复目录为空 则从中转机器中获得合适的大磁盘机器
	if msg.GetRestoreDataTaskReqMsg().GetRestoreIp() == "" || msg.GetRestoreDataTaskReqMsg().GetRestorePath() == "" {
		//选择空闲余量最大的大磁盘机器
		remoteIp, workDir = getRemoteMachine(common.RESTORE, global.DbsEnv(bkInfo.Env))
		targetPath = fmt.Sprintf("%v/%v_%v_%v/%v", workDir, clusterName, clusterId, nodeId, taskId)
		// 若不为分盘打包逻辑，更新选出的机器、恢复目录到数据库中
		if rsType != int32(global.RestorePartition) {
			rsTask := dao_mdc.RSTaskRecord{
				Id:                taskId,
				TargetMachineIp:   remoteIp,
				TargetMachinePath: targetPath,
			}
			_, err = rsTask.UpdateByIndexs([]string{"TargetMachineIp", "TargetMachinePath"})
			if err != nil {
				return err
			}
		}
	} else {
		remoteIp = msg.GetRestoreDataTaskReqMsg().GetRestoreIp()
		targetPath = msg.GetRestoreDataTaskReqMsg().GetRestorePath()
	}
	if remoteIp == "" || targetPath == "" {
		errMsg := fmt.Sprintf("dealRestoreData get remoteIp|targetPath failed,remoteIp=[%v],targetPath=[%v]", remoteIp, targetPath)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//构建拉取恢复数据的rpc请求
	rsReq := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA,
		TaskId:           msg.GetRestoreDataTaskReqMsg().TaskId,
		BaseMessage:      msg.GetBaseMessage(),
		MsgType: &pb_agent.MdcAgentAsyncMsg_DownloadRestoreData{
			DownloadRestoreData: &pb_agent.DownloadRestoreData{
				SourceType:       sourceType,
				BaseDataPath:     dataPath.BasePath,
				IncDataPath:      dataPath.IncDataPath,
				BinlogPath:       dataPath.BinlogPath,
				BinlogSourceType: dataPath.BinlogType,
				TargetPath:       targetPath,
				MdcAgentPort:     msg.GetRestoreDataTaskReqMsg().GetRsAgentPort(),
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// agent端口不为0, 获取其指定agent端口信息
	if msg.GetRestoreDataTaskReqMsg().GetRsAgentPort() != 0 {
		agentAddr = fmt.Sprintf("%s:%d", remoteIp, msg.GetRestoreDataTaskReqMsg().GetRsAgentPort())
	} else {
		// 请求走向大磁盘机器
		agentAddr = fmt.Sprintf("%s:%d", remoteIp, common.Config.AgentPort)
	}

	// 发送消息成功, 新增日志
	if errUpdate := data.UpdateRsTaskExecLog(msg.GetRestoreDataTaskReqMsg().TaskId,
		fmt.Sprintf("start send restore data. BaseMessage=[%+v] GetMsgType=[%+v] RestoreMsg=[%+v]", rsReq.GetBaseMessage(), rsReq.GetMsgType(), msg.GetRestoreDataTaskReqMsg())); errUpdate != nil {
		common.Log.Warn("UpdateRsTaskExecLog failed. err=[%v]", errUpdate)
	}
	// 发送恢复请求给agent
	if _, err := communicator.SendAsyncMsgToDagent(rsReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, rsReq, agentAddr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

//根据恢复时间 计算获得数据来源类型
func calDownloadSourceType(rsTimeStr string) (pb_agent.SourceType, error) {
	currentTime := time.Now()
	currentTime = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.Local)
	rsTime, err := time.ParseInLocation(common.TIME_FORMAT, rsTimeStr, time.Local)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse the recovery time, err=[%v] rsTimeStr=[%v]", err, rsTimeStr)
		common.Log.Error(errMsg)
		return pb_agent.SourceType(-1), err
	}
	rsTime = time.Date(rsTime.Year(), rsTime.Month(), rsTime.Day(), 0, 0, 0, 0, time.Local)
	subDay := int(currentTime.Sub(rsTime).Hours() / 24)
	// 从大磁盘拉取备份
	if subDay <= 1 {
		return pb_agent.SourceType_REMOTE, nil
		// 从dba bos拉取备份
	} else if subDay <= common.Config.DbaBosSaveDays {
		return pb_agent.SourceType_DBA_BOS, nil
		// 从siod bos拉取备份
	} else if subDay <= common.Config.SiodBosSaveDays {
		return pb_agent.SourceType_SIOS_BOS, nil
	}
	// 从磁带库拉去备份
	return pb_agent.SourceType_TAPE_LIB, nil
}

// 根据恢复时间 计算获得数据来源类型
// TODO: fix it 去除此块逻辑，改为从mdc-agent端识别下载地址，进行数据恢复下载。
func getSnapshotSourceType(rsTimeStr string) (pb_agent.SourceType, error) {
	currentTime := time.Now()
	currentTime = time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, time.Local)
	rsTime, err := time.ParseInLocation(common.TIME_FORMAT, rsTimeStr, time.Local)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to parse the recovery time, err=[%v] rsTimeStr=[%v]", err, rsTimeStr)
		common.Log.Error(errMsg)
		return pb_agent.SourceType(-1), err
	}
	rsTime = time.Date(rsTime.Year(), rsTime.Month(), rsTime.Day(), 0, 0, 0, 0, time.Local)
	subDay := int(currentTime.Sub(rsTime).Hours() / 24)
	// 从大磁盘拉取备份
	if subDay <= common.Config.DbaBosSaveDays {
		return pb_agent.SourceType_DBA_BOS, nil
		// 从siod bos拉取备份
	} else if subDay <= common.Config.SiodBosSaveDays {
		return pb_agent.SourceType_SIOS_BOS, nil
	}
	// 从磁带库拉去备份
	return pb_agent.SourceType_TAPE_LIB, nil
}

//获得用来恢复的数据目录
func getSourceDataPath(clusterId int64, nodeId int64, rsTime string,
	sourceType pb_agent.SourceType, rsTimeType int32, clusterBkType int32) (sourcePath *restoreDataPath, err error) {
	if rsTimeType == int32(common.Snapshot) {
		//如果是恢复到天级快照
		sourcePath, err = getRsSnapshotDataPath(clusterId, nodeId, rsTime, sourceType, clusterBkType)
	} else if rsTimeType == int32(common.AnyTime) {
		//如果是恢复到具体时间
		sourcePath, err = getRsAnyTimeDataPath(clusterId, nodeId, rsTime, sourceType, clusterBkType)
	}
	common.Log.Warn("getSourceDataPath,sourcePath=[%v]", sourcePath)
	return sourcePath, err
}

//获得恢复到天级快照的数据目录
func getRsSnapshotDataPath(clusterId int64, nodeId int64, rsTime string, sourceType pb_agent.SourceType, clusterBkType int32) (*restoreDataPath, error) {
	sourcePath := new(restoreDataPath)
	//如果是恢复到天级快照
	//获得全备数据目录
	err, xtraBaseTask := data.GetLastXtraBaseTask(clusterId, nodeId, rsTime)
	if err != nil || xtraBaseTask == nil {
		errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
		sourcePath.BasePath = xtraBaseTask.BkBosPath
	} else if sourceType == pb_agent.SourceType_SIOS_BOS {
		sourcePath.BasePath = xtraBaseTask.SiodFilePath
	} else {
		//获得tape 存储路径
		sourcePath.BasePath = xtraBaseTask.SiodTapePath
	}
	if clusterBkType == int32(common.IncHotBk) {
		var LsnToTask = xtraBaseTask
		//如果是全备+增备集群 获得增备数据目录
		err, xtraTaskList := data.GetRangeXtraList(clusterId, nodeId, xtraBaseTask.BkCreateTime, rsTime)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		common.Log.Info("[getRsSnapshotDataPath] base=[%+v] xtraTaskList=[%+v]", xtraBaseTask, xtraTaskList)
		for _, task := range xtraTaskList {
			if task.BkType == int32(common.FullHotBk) {
				common.Log.Warn("xtraTask taskId =[%v],bkType=[%v],bkExecStatus=[%v]", task.TaskId, task.BkType, task.BkExecStatus)
				continue
			}
			// 对比LSN
			if LsnToTask.LsnTo == task.LsnFrom {
				LsnToTask = task
				sourcePath.IncDataPath = append(sourcePath.IncDataPath, task.BkBosPath)
			} else {
				common.Log.Error("lsn check failed. LsnTo=[%+v] LsnFrom=[%+v]", LsnToTask, task)
				return sourcePath, fmt.Errorf("lsn check failed. LsnTo=[%+v] LsnFrom=[%+v]", LsnToTask.LsnTo, task.LsnFrom)
			}
		}

	}
	return sourcePath, nil
}

//获得恢复到具体时间点的数据目录
func getRsAnyTimeDataPath(clusterId int64, nodeId int64, rsTime string,
	sourceType pb_agent.SourceType, clusterBkType int32) (*restoreDataPath, error) {
	sourcePath := new(restoreDataPath)
	//如果是恢复到具体时间
	//如果是全备集群,获得全备数据目录
	err, xtraBaseTask := data.GetLastXtraBaseTask(clusterId, nodeId, rsTime)
	if err != nil || xtraBaseTask == nil {
		errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
		sourcePath.BasePath = xtraBaseTask.BkBosPath
	} else if sourceType == pb_agent.SourceType_SIOS_BOS {
		sourcePath.BasePath = xtraBaseTask.SiodFilePath
	} else {
		//获得tape 存储路径
		sourcePath.BasePath = xtraBaseTask.SiodTapePath
	}
	//如果是增备集群,依次获得增备数据目录
	var xtraBaseTaskTime string
	xtraTaskList := make([]*dao_mdc.BkXtraTaskExecRecord, 0)
	if clusterBkType == int32(common.IncHotBk) {
		//如果是全备+增备集群 获得增备数据目录
		err, xtraTaskList = data.GetRangeXtraList(clusterId, nodeId, xtraBaseTask.BkCreateTime, rsTime)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		if xtraTaskList != nil && len(xtraTaskList) != 0 {
			common.Log.Warn("GetRangeXtraList = [%v]", xtraTaskList)
			for _, task := range xtraTaskList {
				// 备份集合类型为全备跳过
				if task.BkType == int32(common.FullHotBk) {
					xtraBaseTaskTime = task.BkCreateTime
					common.Log.Warn("xtraTask taskId =[%v],bkType=[%v],bkExecStatus=[%v]", task.TaskId, task.BkType, task.BkExecStatus)
					continue
				}
				// 备份状态为上传dba_siod bos成功且状态不为上传DBA bos失败或上传siod bos失败即为有效备份集合
				if taskFlowStatusIsOK(task.BkExecStatus) {
					if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
						sourcePath.IncDataPath = append(sourcePath.IncDataPath, task.BkBosPath)
					} else if sourceType == pb_agent.SourceType_SIOS_BOS {
						sourcePath.IncDataPath = append(sourcePath.IncDataPath, task.SiodFilePath)
					} else {
						//获得tape 存储路径
						sourcePath.IncDataPath = append(sourcePath.IncDataPath, task.SiodTapePath)
					}
				}
			}
		}
	}
	//获得binlog目录
	//备份集合只有1个(1个备份集合存在只有base全备；>1则是全备+增备都存在情况)
	if xtraTaskList != nil && len(xtraTaskList) > 1 {
		common.Log.Warn("getRsAnyTimeDataPath, last xtraBaseTask BkCreateTime=[%+v]", xtraBaseTaskTime)
		// 获取最近一次备份记录信息
		err, xtraBinlogTask := data.GetNextXtraTask(clusterId, nodeId, xtraBaseTaskTime)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		// 备份执行结束时间小于目标恢复时间时则去大磁盘获取未打包binlog信息
		if xtraBinlogTask.BkEndTime <= rsTime {
			sourcePath.BinlogType = pb_agent.SourceType_REMOTE
			statusList := []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}
			err, recordList := data.GetBinlogRecordList(clusterId, nodeId, statusList)
			if err != nil {
				errMsg := fmt.Sprintf("GetBinlogRecordList failed, error=[%v],ClusterId=[%v]", err, clusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			for _, val := range recordList {
				binlogList := strings.Split(val.BinlogFileName, ",")
				if len(binlogList) == 1 {
					sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogList[0])
					sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
				}
				for _, binlogName := range binlogList {
					sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogName)
					sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
				}
			}
			return sourcePath, nil
			// 否则当前集合备份集合中就存在恢复时间所需binlog
		} else {
			if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
				sourcePath.BinlogType = pb_agent.SourceType_DBA_BOS
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, xtraBinlogTask.BkBosPath)
			} else if sourceType == pb_agent.SourceType_SIOS_BOS {
				sourcePath.BinlogType = pb_agent.SourceType_SIOS_BOS
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, xtraBinlogTask.SiodFilePath)
			} else {
				//获得tape 存储路径
				sourcePath.BinlogType = pb_agent.SourceType_TAPE_LIB
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, xtraBinlogTask.SiodTapePath)
			}
			return sourcePath, nil
		}
		// 备份集合中只有全备
	} else if xtraBaseTask != nil {
		common.Log.Warn("getRsAnyTimeDataPath,last xtraBaseTask=[%+v]", xtraBaseTask)
		// 备份集合只有一个base 确认增备记录信息是否为空
		// 获取全备之后的增量备份
		err, incrTaskInfo := data.GetIncrTask(clusterId, nodeId, xtraBaseTask.BkCreateTime)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		// 备份结束时间小于恢复时间并且全备之后增量记录信息为空则去大磁盘拉去恢复所需binlog
		if xtraBaseTask.BkEndTime <= rsTime && incrTaskInfo == nil {
			sourcePath.BinlogType = pb_agent.SourceType_REMOTE
			statusList := []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}
			err, recordList := data.GetBinlogRecordList(clusterId, nodeId, statusList)
			if err != nil {
				errMsg := fmt.Sprintf("GetBinlogRecordList failed, error=[%v],ClusterId=[%v]", err, clusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			for _, val := range recordList {
				binlogList := strings.Split(val.BinlogFileName, ",")
				if len(binlogList) == 1 {
					sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogList[0])
					sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
				}
				for _, binlogName := range binlogList {
					sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogName)
					sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
				}
			}
			return sourcePath, nil
			// 否则增量集合中存在恢复所需binlog;
		} else {
			if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
				sourcePath.BinlogType = pb_agent.SourceType_DBA_BOS
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, incrTaskInfo.BkBosPath)
			} else if sourceType == pb_agent.SourceType_SIOS_BOS {
				sourcePath.BinlogType = pb_agent.SourceType_SIOS_BOS
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, incrTaskInfo.SiodFilePath)
			} else {
				//获得tape 存储路径
				sourcePath.BinlogType = pb_agent.SourceType_TAPE_LIB
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, incrTaskInfo.SiodTapePath)
			}
			return sourcePath, nil
		}
	} else {
		sourcePath.BinlogType = pb_agent.SourceType_REMOTE
		statusList := []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}
		err, recordList := data.GetBinlogRecordList(clusterId, nodeId, statusList)
		if err != nil {
			errMsg := fmt.Sprintf("GetBinlogRecordList failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		for _, val := range recordList {
			binlogList := strings.Split(val.BinlogFileName, ",")
			if len(binlogList) == 1 {
				sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogList[0])
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
			}
			for _, binlogName := range binlogList {
				sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogName)
				sourcePath.BinlogPath = append(sourcePath.BinlogPath, sPath)
			}
		}
		return sourcePath, nil
	}
	return sourcePath, nil
}

//处理解析binlog任务
func dealParseBinlog(msg *pb_server.XwebPlusFrontAsyncMsg) error {
	var agentAddr string
	startTime := msg.GetParseBinlogTaskReqMsg().StartTime
	endTime := msg.GetParseBinlogTaskReqMsg().EndTime
	clusterId := msg.GetBaseMessage().GetClusterId()
	nodeId := msg.GetBaseMessage().GetNodeId()
	clusterName := msg.GetBaseMessage().GetClusterName()
	taskId := msg.GetParseBinlogTaskReqMsg().TaskId
	// 计算获得用来解析的数据目标存放地址
	sourceType, err := calDownloadSourceType(startTime)
	if err != nil {
		errMsg := fmt.Sprintf("calDownloadSourceType failed, error=[%v],ClusterId=[%v], ClusterName=[%v],NodeId=[%v]", err, clusterId, msg.GetBaseMessage().GetClusterName(), msg.GetBaseMessage().GetNodeId())
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得用来解析的数据目录  xtraDataPath(已打包完成的数据包), remoteBinlogPath(为完成打包只存在大磁盘机器)
	xtraDataPath, remoteBinlogPath, err := getbinlogDataPath(clusterId, nodeId, startTime, endTime, sourceType)
	if err != nil {
		errMsg := fmt.Sprintf("getbinlogDataPath failed, error=[%v],ClusterId=[%v], ClusterName=[%v],NodeId=[%v]", err, clusterId, msg.GetBaseMessage().GetClusterName(), msg.GetBaseMessage().GetNodeId())
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//获得集群备份类型
	err, bkInfo := data.GetBkTaskInfo(clusterId, nodeId)
	if err != nil || bkInfo == nil {
		errMsg := fmt.Sprintf("Get backup task info failed, error=[%v],ClusterId=[%v], NodeId=[%v]", err, clusterId, nodeId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//选择空闲余量最大的大磁盘机器
	remoteIp, _ := getRemoteMachine(common.RESTORE, global.DbsEnv(bkInfo.Env))
	if remoteIp == "" {
		errMsg := fmt.Sprintf("dealParseBinlog get remoteIp failed,remoteIp=[%v]", remoteIp)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//构建下载数据rpc请求
	rsReq := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_BINLOG_DATA,
		TaskId:           msg.GetParseBinlogTaskReqMsg().TaskId,
		BaseMessage:      msg.GetBaseMessage(),
		MsgType: &pb_agent.MdcAgentAsyncMsg_DownloadBinlogData{
			DownloadBinlogData: &pb_agent.DownloadBinlogData{
				SourceType:     sourceType,
				DataPath:       xtraDataPath,
				RemoteDataPath: remoteBinlogPath,
				TargetPath:     fmt.Sprintf("%v/%v_%v_%v/%v", common.RsPrefixPathName, clusterName, clusterId, nodeId, taskId),
				MdcAgentPort:   msg.GetParseBinlogTaskReqMsg().GetTargetAgentPort(),
			},
		},
	}
	// 获取发起rpc任务的rpc地址及执行任务的agentIP
	// agent端口不为0, 获取其指定agent端口信息
	if msg.GetParseBinlogTaskReqMsg().GetTargetAgentPort() != 0 {
		agentAddr = fmt.Sprintf("%s:%d", remoteIp, msg.GetParseBinlogTaskReqMsg().GetTargetAgentPort())
	} else {
		// 请求走向DBA恢复机器上
		agentAddr = fmt.Sprintf("%s:%d", remoteIp, common.Config.AgentPort)
	}
	if _, err := communicator.SendAsyncMsgToDagent(rsReq, agentAddr); err != nil {
		errMsg := fmt.Sprintf("SendAsyncMsgToDagent failed,err=[%v],req=[%v],agentAddr=[%v]", err, rsReq, agentAddr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//获得用来解析binlog的数据目录
func getbinlogDataPath(clusterId int64, nodeId int64, startTime string, endTime string, sourceType pb_agent.SourceType) (xtraDataPath []string, remotebinlogPath []string, err error) {
	//最近一次备份完成的xtraBackup备份任务
	err, xtraTask := data.GetClusterLatestSuccessRecord(clusterId, nodeId, pb_server.XtraTaskStatus_UploadDbaBosSuccess)
	if err != nil || xtraTask == nil {
		common.Log.Warn("Failed to get xtra_task info from db. ClusterId=[%v] err=[%v]",
			clusterId, err)
		return nil, nil, err
	}

	// 备份流转状态判断
	if xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_UploadDbaBosFail) ||
		xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_UploadSiodBosReicer) ||
		xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_DbaBosDataClean) ||
		xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_RegisterSiodBosFail) ||
		xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_UploadSiodBos) ||
		xtraTask.BkExecStatus == int32(pb_server.XtraTaskStatus_XtraTaskAbandon) {
		errMsg := fmt.Sprintf("xtra_task status is not ok. ClusterId=[%v]", clusterId)
		common.Log.Error(errMsg)
		return nil, nil, errors.New(errMsg)
	}
	// 确认备份任务创建时间大于恢复恢复时间
	if endTime < xtraTask.BkCreateTime {
		//获得xtra备份数据目录
		xtraDataPath, err = getXtraDataPath(clusterId, nodeId, startTime, endTime, sourceType)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, nil, err
		}
		// 备份集合任务创建时间大于恢复开始时间 && 恢复结束时间大于备份节后创建时间
	} else if startTime < xtraTask.BkCreateTime && endTime > xtraTask.BkCreateTime {
		//获得xtra备份数据目录
		xtraDataPath, err = getXtraDataPath(clusterId, nodeId, startTime, endTime, sourceType)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, nil, err
		}
		//获得大磁盘数据
		if sourceType == pb_agent.SourceType_REMOTE {
			//获得大磁盘机器上的binlog
			remotebinlogPath, err = getRemoteBinlogPath(clusterId, nodeId, startTime, endTime)
			if err != nil {
				errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
				common.Log.Warn(errMsg)
				return nil, nil, err
			}
		}
		//恢复时间开始时间大于任务创建时间
	} else if xtraTask.BkCreateTime < startTime {
		//获得大磁盘机器上的binlog
		remotebinlogPath, err = getRemoteBinlogPath(clusterId, nodeId, startTime, endTime)
		if err != nil {
			errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v]", err, clusterId)
			common.Log.Warn(errMsg)
			return nil, nil, err
		}
	}
	return xtraDataPath, remotebinlogPath, err
}

// binlog恢复过程中获得xtra数据目录
func getXtraDataPath(clusterId int64, nodeId int64, startTime string, endTime string, sourceType pb_agent.SourceType) (binlogDataPath []string, err error) {
	err, xtraTaskList := data.GetRangeXtraList(clusterId, nodeId, startTime, endTime)
	if err != nil {
		errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v],nodeId=[%v],startTime=[%v],endTime=[%v]", err, clusterId, nodeId, startTime, endTime)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	if xtraTaskList != nil && len(xtraTaskList) > 0 {
		for _, task := range xtraTaskList {
			if taskFlowStatusIsOK(task.BkExecStatus) {
				if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
					binlogDataPath = append(binlogDataPath, task.BkBosPath)
				} else if sourceType == pb_agent.SourceType_SIOS_BOS {
					binlogDataPath = append(binlogDataPath, task.SiodFilePath)
				} else {
					//获得tape 存储路径
					binlogDataPath = append(binlogDataPath, task.SiodTapePath)
				}
			}
		}
	}
	var nextCreateTime string
	if xtraTaskList != nil && len(xtraTaskList) > 0 {
		nextCreateTime = xtraTaskList[len(xtraTaskList)-1].BkCreateTime
	} else {
		nextCreateTime = startTime
	}
	err, xtraBinlogTask := data.GetNextXtraTask(clusterId, nodeId, nextCreateTime)
	if err != nil || xtraBinlogTask == nil {
		errMsg := fmt.Sprintf("Get xtra task info failed, error=[%v],ClusterId=[%v],nodeId=[%v],startTime=[%v],endTime=[%v],nextEndTime=[%v]", err, clusterId, nodeId, startTime, endTime, nextCreateTime)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	if sourceType == pb_agent.SourceType_REMOTE || sourceType == pb_agent.SourceType_DBA_BOS {
		binlogDataPath = append(binlogDataPath, xtraBinlogTask.BkBosPath)
	} else if sourceType == pb_agent.SourceType_SIOS_BOS {
		binlogDataPath = append(binlogDataPath, xtraBinlogTask.SiodFilePath)
	} else {
		//获得tape 存储路径
		binlogDataPath = append(binlogDataPath, xtraBinlogTask.SiodTapePath)
	}
	return
}

//获得大磁盘机器上的binlog存储目录
func getRemoteBinlogPath(clusterId int64, nodeId int64, startTime string, endTime string) (binlogPathList []string, err error) {
	//查询获得大磁盘机器上的binlog
	statusList := []pb_server.BinlogRecordStatus{pb_server.BinlogRecordStatus_UploadRemoteSuccess}
	err, recordList := data.GetBinlogRecordRangeList(clusterId, nodeId, statusList, startTime, endTime)
	if err != nil || len(recordList) == 0 {
		errMsg := fmt.Sprintf("Get remote machine binlog info failed, error=[%v],ClusterId=[%v],nodeId=[%v],startTime=[%v],endTime=[%v]", err, clusterId, nodeId, startTime, endTime)

		common.Log.Warn(errMsg)
		return nil, err
	}
	//获得binlog存储目录
	for _, val := range recordList {
		binlogList := strings.Split(val.BinlogFileName, ",")
		if len(binlogList) == 1 {
			sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogList[0])
			binlogPathList = append(binlogPathList, sPath)
		}
		for _, binlogName := range binlogList {
			sPath := fmt.Sprintf("%v:%v/%v", val.RemoteIp, val.RemotePath, binlogName)
			binlogPathList = append(binlogPathList, sPath)
		}
	}
	return binlogPathList, nil
}

// 任务状态断言
func taskFlowStatusIsOK(taskStatus int32) bool {
	if taskStatus > int32(pb_server.XtraTaskStatus_UploadDbaBosSuccess) {
		if taskStatus != int32(pb_server.XtraTaskStatus_UploadSiodBosFail) ||
			taskStatus != int32(pb_server.XtraTaskStatus_UploadSiodBosReicer) ||
			taskStatus != int32(pb_server.XtraTaskStatus_UploadDbaBosFail) ||
			taskStatus != int32(pb_server.XtraTaskStatus_UploadSiodBosReicer) ||
			taskStatus != int32(pb_server.XtraTaskStatus_DbaBosDataClean) ||
			taskStatus != int32(pb_server.XtraTaskStatus_RegisterSiodBosFail) ||
			taskStatus != int32(pb_server.XtraTaskStatus_UploadSiodBos) ||
			taskStatus != int32(pb_server.XtraTaskStatus_XtraTaskAbandon) {
			return true
		}
	}
	return false
}
