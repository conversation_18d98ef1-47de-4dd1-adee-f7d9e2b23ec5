package worker

import (
	"mdc-server/common"
	"testing"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/agiledragon/gomonkey"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-server/communicator"
)

func TestDealRestoreData(t *testing.T) {
	Convey("测试dealRestoreData", t, func() {
		patches := ApplyFunc(communicator.SendAsyncMsgToDagent, func(data pb_agent.MdcAgentAsyncMsg, clientIp string) (*pb_agent.MdcAgentAsyncRespons, error) {
			return nil, nil
		})
		defer patches.Reset()
		msg := &pb_server.XwebPlusFrontAsyncMsg{
			TaskType: pb_server.MdcServerTaskType_RESTORE_DATA,
			BaseMessage: &pb_server.MdcBaseMessage{
				ClusterName: "test",
				ClusterId:   193827,
				NodeId:      1,
			},
		}
		err := dealRestoreData(msg)
		So(err, ShouldBeNil)
	})
}

func TestCalDownloadSourceType(t *testing.T) {
	Convey("测试calDownloadSourceType", t, func() {
		common.Config.DbaBosSaveDays = 30
		common.Config.SiodBosSaveDays = 180
		valueType, err := calDownloadSourceType("2021-08-30 21:00:00")
		So(err, ShouldBeNil)
		So(valueType, ShouldEqual, pb_agent.SourceType_DBA_BOS)

		valueType, err = calDownloadSourceType("2021-03-30 21:00:00")
		So(err, ShouldBeNil)
		So(valueType, ShouldEqual, pb_agent.SourceType_SIOS_BOS)
	})
}

func TestGetRsSnapshotDataPath(t *testing.T) {
	Convey("测试getRsSnapshotDataPath", t, func() {
		_, err := getRsSnapshotDataPath(1, 1, "2021-08-30 12:00:00", pb_agent.SourceType_DBA_BOS, 1)
		So(err, ShouldNotBeNil)
	})
}

func TestGetRsAnyTimeDataPath(t *testing.T) {
	Convey("测试getRsAnyTimeDataPath", t, func() {
		_, err := getRsAnyTimeDataPath(1, 1, "2021-08-30 12:00:00", pb_agent.SourceType_DBA_BOS, 1)
		So(err, ShouldNotBeNil)
	})

	Convey("测试getRsAnyTimeDataPath", t, func() {
		_, err := getRsAnyTimeDataPath(12345, 111, "2021-11-08 12:00:00", pb_agent.SourceType_REMOTE, 1)
		So(err, ShouldNotBeNil)
	})
}

func TestGetbinlogDataPath(t *testing.T) {
	Convey("测试getbinlogDataPath", t, func() {
		_, _, err := getbinlogDataPath(1, 1, "2021-08-29 12:00:00", "2021-08-30 12:00:00", pb_agent.SourceType_DBA_BOS)
		So(err, ShouldNotBeNil)
	})
}

func TestGetXtraDataPath(t *testing.T) {
	Convey("测试getXtraDataPath", t, func() {
		_, err := getXtraDataPath(1, 1, "2021-08-30 12:00:00", "2021-08-30 12:00:00", pb_agent.SourceType_DBA_BOS)
		So(err, ShouldNotBeNil)
	})
}

func TestGetRemoteBinlogPath(t *testing.T) {
	Convey("测试getRemoteBinlogPath", t, func() {
		_, err := getRemoteBinlogPath(1, 1, "2021-08-30 12:00:00", "2021-08-30 12:00:00")
		So(err, ShouldBeNil)
	})
}
