package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"runtime"
	"syscall"
	"time"

	"mdc-server/common"
	"mdc-server/communicator"
	"mdc-server/data"
	"mdc-server/scheduler"
	"mdc-server/worker"
)

var (
	DXMDate    string
	DXMVersion string
)

func main() {
	if err := common.InitControlLog(); nil != err {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc server start failed] Init control log failed. err=%v \n",
			time.Now().Format(common.TIME_FORMAT), err))
		os.Exit(-1)
	}
	// 控制main使用cpu的总数,留2颗cpu，其他全部都给程序用
	runtime.GOMAXPROCS(runtime.NumCPU() - 2)
	// 传入配置文件
	conf := flag.String("config", "mdc-server.yaml", "mdc server configure file.")
	// 解析传入的参数
	flag.Parse()

	fmt.Printf("Git commit:%s \n", DXMVersion)
	fmt.Printf("Build time:%s \n", DXMDate)

	// 初始化配置文件
	if err := common.ParseConfig(*conf); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc server start failed]"+
			" Parse config file failed. err=%v \n", time.Now().Format(common.TIME_FORMAT), err))
		fmt.Fprintln(os.Stderr, "[mdc server] failed to parse config file: "+err.Error())
		os.Exit(-1)
	}
	// 初始化DAO
	if err := data.InitDao(); err != nil {
		fmt.Fprintln(os.Stderr, "[mdc server] failed to Init dao: "+err.Error())
		os.Exit(-1)
	}
	// 初始化msg buffer
	common.InitMsgBuffer()
	//初始化邮件配置
	common.InitEmail()
	//初始化获取uuap配置
	common.InitUuap()
	// 注册mdc server信息
	if err := scheduler.MsRegister(common.Config.MdcServerId); err != nil {
		os.Stderr.WriteString(fmt.Sprintf("%v [mdc server start failed]"+
			" Register ms info failed. err=%v \n", time.Now().Format(common.TIME_FORMAT), err))
		fmt.Fprintln(os.Stderr, "[mdc server] failed to register mdc server: "+err.Error())
		os.Exit(-1)
	}
	// 启动监听端口
	common.Waitgroup.Add(1)
	// 初始化grpMdcServer
	grpcServer := communicator.NewGrpcServer()
	go communicator.StartGrpcServer(common.Waitgroup, grpcServer)

	// 启动worker开始处理消息
	common.Waitgroup.Add(1)
	go worker.StartWorker(common.Config.WorkerNumber, common.Config.WorkerChanTimeOut, common.Waitgroup)

	// 启动4个定时timer
	common.Waitgroup.Add(4)
	go scheduler.Start(common.Waitgroup)

	// 初始化cron定时任务
	common.Waitgroup.Add(1)
	go scheduler.StartInitCron(common.Waitgroup)

	os.Stdout.WriteString(fmt.Sprintf("%v [mdc server start success] \n", time.Now().Format(common.TIME_FORMAT)))
	// 预定义需要接收的终端指令
	sc := make(chan os.Signal)
	signal.Notify(sc,
		syscall.SIGINT,
		syscall.SIGTERM,
	)
	// 接收运行时的终端指令
	for {
		time.Sleep(1 * time.Second)
		switch sig := <-sc; sig {
		case syscall.SIGINT, syscall.SIGTERM:
			os.Stdout.WriteString(fmt.Sprintf("%v [mdc server receive exit signal]"+
				" Signal=%v \n", time.Now().Format(common.TIME_FORMAT), sig))
			common.Log.Notice("[Mdc server] user called to stop mdc server. signal:%v", sig)
			// 关闭listener的协程
			communicator.StopGrpcServer(common.Waitgroup, grpcServer)
			// 关闭scheduler的协程
			scheduler.Stop()
			// 关闭worker模块的协程
			worker.StopWorker()
			// 等待主协程退出
			common.Waitgroup.Wait()
			os.Exit(0)
		default:
			os.Stderr.WriteString(fmt.Sprintf("%v [mdc server receive unknown signal]"+
				" Signal=%v \n", time.Now().Format(common.TIME_FORMAT), sig))
			common.Log.Warning("[Mdc server] command is not syscall.SIGINT or syscall.SIGTERM.")
			continue
		}
	}
}
