module mdc-server

go 1.21

require (
	dt-common v0.0.0-00010101000000-000000000000
	dxm/siod-cloud/go-common-lib v0.0.0-00010101000000-000000000000
	github.com/agiledragon/gomonkey v2.0.2+incompatible
	github.com/astaxie/beego v1.12.3
	github.com/jordan-wright/email v4.0.1-0.20210109023952-943e75fe5223+incompatible
	github.com/robfig/cron/v3 v3.0.1
	github.com/smartystreets/goconvey v1.7.2
	google.golang.org/grpc v1.54.0
	gopkg.in/yaml.v2 v2.4.0
)

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.2.0 // indirect
	github.com/go-sql-driver/mysql v1.5.0 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/gopherjs/gopherjs v0.0.0-20181017120253-0766667cb4d1 // indirect
	github.com/hashicorp/golang-lru v0.5.4 // indirect
	github.com/jtolds/gls v4.20.0+incompatible // indirect
	github.com/matttproud/golang_protobuf_extensions v1.0.1 // indirect
	github.com/pingcap/errors v0.11.4 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/prometheus/client_golang v1.7.0 // indirect
	github.com/prometheus/client_model v0.2.0 // indirect
	github.com/prometheus/common v0.10.0 // indirect
	github.com/prometheus/procfs v0.1.3 // indirect
	github.com/shiena/ansicolor v0.0.0-20151119151921-a422bbe96644 // indirect
	github.com/smartystreets/assertions v1.2.0 // indirect
	golang.org/x/crypto v0.0.0-20191011191535-87dc89f01550 // indirect
	golang.org/x/net v0.8.0 // indirect
	golang.org/x/sys v0.6.0 // indirect
	golang.org/x/text v0.8.0 // indirect
	google.golang.org/genproto v0.0.0-20230110181048-76db0878b65f // indirect
	google.golang.org/protobuf v1.28.1 // indirect
)

replace (
	dt-common => gitlab.duxiaoman-int.com/dba/dt-common v0.0.0-20230222022631-4109628d0523
	dxm/siod-cloud/go-common-lib => gitlab.duxiaoman-int.com/siod-cloud/go-common-lib v0.0.0-20221118145531-a0e4ed8177f2
)
